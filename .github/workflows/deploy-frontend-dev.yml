name: Deploy Frontend Dev to EC2

on:
  pull_request:
    branches:
      - dev
  push:
    branches:
      - dev

jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
      AWS_REGION: us-east-1

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install Dependencies
        run: npm install --force

      - name: Build App
        run: npm run build

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_DEV }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Set up SSH Key
        run: |
          echo "${{ secrets.EC2_KEY_FRONTEND_DEV }}" | base64 -d > ec2_key.pem
          chmod 600 ec2_key.pem

      - name: Deploy to EC2 via SSH
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.EC2_HOST_FRONTEND_DEV }}
          username: ubuntu
          key_path: ec2_key.pem
          script: |
            mkdir -p /home/<USER>/frontend
            cd /home/<USER>/frontend
            git reset --hard origin/dev
            git pull origin dev --no-verify
            npm install 
            rm -rf .next
            npm run build
            sudo systemctl restart tradereply.service

      - name: Remove SSH Key
        run: rm -f ec2_key.pem
