<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TradeForm extends Model
{
    use HasFactory;

    protected $fillable = [
        'trade_id',
        'type',
        'index'
    ];

    public function trade()
    {
        return $this->belongsTo(Trade::class);
    }

    public function sections()
    {
        return $this->hasMany(TradeFormSection::class);
    }
}

