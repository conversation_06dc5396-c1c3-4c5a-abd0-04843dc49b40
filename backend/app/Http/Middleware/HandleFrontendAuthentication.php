<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class HandleFrontendAuthentication
{
    /**
     * Public routes that don't require authentication
     */
    protected $publicRoutes = [
        '/',
        '/pricing',
        '/education',
        '/blog',
        '/privacy',
        '/terms',
        '/cookies',
        '/brokers',
        '/help',
        '/contact',
        '/login',
        '/signup',
        '/forget-password',
        '/verify-email',
        '/home'
    ];

    /**
     * Route prefixes that require authentication
     */
    protected $protectedPrefixes = [
        '/user',
        '/account', 
        '/dashboard',
        '/super-admin',
        '/not-found'
    ];

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only handle frontend routes (not API routes)
        if ($request->is('api/*')) {
            return $next($request);
        }

        $path = '/' . ltrim($request->path(), '/');
        
        // Check if route requires authentication
        if ($this->requiresAuthentication($path)) {
            // Check if user is authenticated via Sanctum token
            if (!Auth::guard('sanctum')->check()) {
                // For AJAX requests, return JSON
                if ($request->expectsJson()) {
                    return response()->json([
                        'message' => 'Authentication required',
                        'redirect' => '/login'
                    ], 401);
                }
                
                // For regular requests, redirect to login
                return redirect('/login');
            }
        }

        return $next($request);
    }

    /**
     * Check if a route requires authentication
     */
    protected function requiresAuthentication(string $path): bool
    {
        // Check if it's a public route
        if (in_array($path, $this->publicRoutes)) {
            return false;
        }

        // Check if it starts with a protected prefix
        foreach ($this->protectedPrefixes as $prefix) {
            if (str_starts_with($path, $prefix)) {
                return true;
            }
        }

        // Default to public for unmatched routes
        return false;
    }
}
