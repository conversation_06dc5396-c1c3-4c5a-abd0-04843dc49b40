<?php

use App\Http\Controllers\Admin\ArticleController;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\EducationController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\ThirdPartyProvidersController;
use App\Http\Controllers\Dashboard\TradeBuilderController;
use App\Http\Controllers\Dashboard\PortfolioManagerController;
use App\Http\Controllers\PlanController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\PhoneController;
use App\Http\Controllers\SecurityVerificationController;
use App\Http\Controllers\ZendeskProxyController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/


Route::get('/featured-resource', [ArticleController::class, 'featured_resource']);


Route::prefix('search')->group(function () {
    Route::get('/home', [SearchController::class, 'home']);
    Route::get('/', [SearchController::class, 'Search']);
    Route::get('/blog-sitemap', [SearchController::class, 'blogSitemap']);
    Route::get('/{blog:slug}', [SearchController::class, 'show']);
});


Route::match(['get', 'post'], '/zendesk-proxy/{path}', [ZendeskProxyController::class, 'handle'])
    ->where('path', '.*');


Route::get('/blog-sitemap', [ArticleController::class, 'blogSitemap']);
Route::get('/article/', [ArticleController::class, 'index']);
Route::get('/article/{type}/{slug}', [ArticleController::class, 'show'])
    ->where('type', '^(blog|education)$');
Route::post('education/{slug}/progress', [ArticleController::class, 'updateProgress']);


Route::prefix('category')->group(function () {
    Route::get('/{category:slug?}', [CategoryController::class, 'index']);
    Route::get('/page/{page}', [CategoryController::class, 'index'])->where('page', '[0-9]+');
});


Route::prefix('/stripe')->group(function () {
    Route::get('plans', [PlanController::class, 'index']);

    Route::post('/subscribe', [SubscriptionController::class, 'createSubscription']);
    Route::post('/cancel-subscription', [SubscriptionController::class, 'cancelSubscription']);
});


Route::apiResource('thirdpartyprovider', ThirdPartyProvidersController::class);



//Route::middleware(['BearerTokenSanctum','auth:sanctum'])->group(function () {
Route::middleware('auth:sanctum')->group(function () {

    // Security verification routes (authenticated users only)
    Route::prefix('security-verification')->group(function () {
        Route::post('/send-code', [SecurityVerificationController::class, 'sendVerificationCode']);
        Route::post('/verify-code', [SecurityVerificationController::class, 'verifyCode']);
        Route::post('/resend-code', [SecurityVerificationController::class, 'resendCode']);
        Route::get('/status', [SecurityVerificationController::class, 'checkSecurityStatus']);
        Route::get('/config', [SecurityVerificationController::class, 'getSecurityConfig']);
        Route::post('/test-email', [SecurityVerificationController::class, 'testEmail']); // For debugging
        Route::post('/debug-redirect', [SecurityVerificationController::class, 'debugRedirect']); // For debugging
    });


    Route::get('/account', [UserController::class, 'index'])->name('account.index');
    Route::put('/account/update/{user}', [UserController::class, 'update'])->name('account.update');
    Route::post('/account/{user}/email-update', [UserController::class, 'updateEmail'])->name('account.email.update');

    // Phone number management routes (simplified - no verification)
    Route::prefix('phone')->group(function () {
        Route::post('/setup', [PhoneController::class, 'store'])->name('phone.setup');
        Route::delete('/remove', [PhoneController::class, 'destroy'])->name('phone.remove');
    });

    Route::prefix('super-admin/category')->group(function () {
        Route::get('/categories-list', [CategoryController::class, 'categoriesList']);
        Route::post('/store', [CategoryController::class, 'store']);
        Route::get('/{category:slug}/edit', [CategoryController::class, 'edit']);
        Route::put('/{category:slug}', [CategoryController::class, 'update']);
        Route::delete('/{category:slug}', [CategoryController::class, 'destroy']);
    });

    Route::prefix('super-admin/articles')->group(function () {
        Route::get('/list/{type}', [ArticleController::class, 'articlesList'])->where(['type' => 'blog|education']);
        Route::get('/create', [ArticleController::class, 'create']);
        Route::post('/store', [ArticleController::class, 'store']);
        Route::put('/{article}', [ArticleController::class, 'update']);
        Route::delete('/{id}', [ArticleController::class, 'destroy'])->where('id', '[0-9]+');
        Route::get('/{type}/{article}/edit', [ArticleController::class, 'edit'])->where(['type' => 'blog|education']);
    });

    Route::prefix('trade')->group(function () {
        Route::get('/', [TradeBuilderController::class, 'index']);
        Route::get('fetch/fields', [TradeBuilderController::class, 'fetchAllFields']);
        Route::post('store', [TradeBuilderController::class, 'store']);
    });

    Route::prefix('portfolio')->group(function () {
        Route::get('/', [PortfolioManagerController::class, 'index']);
        Route::post('store', [PortfolioManagerController::class, 'store']);
        Route::post('delete', [PortfolioManagerController::class, 'deleteAllEntries']);
    });

    Route::post('/subscribe', [PlanController::class, 'createCheckoutSession']);
    Route::post('/confirm/subscribe', [PlanController::class, 'confirmSubscription']);
});

Route::post('trade/calculate', [TradeBuilderController::class, 'calculateFormulas']);
