<?php

use App\Http\Controllers\Auth\OauthController;
use App\Http\Controllers\PlanController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Resources\BlogResource;
use App\Http\Resources\CategoryResource;
use App\Models\Article;
use App\Models\Blog;
use App\Models\Education;
use App\Models\Category;
use App\Models\FieldDefinition;
use App\Models\PortfolioFieldDefinition;
use App\Models\TradeFieldDefinition;
use App\Models\TransactionFieldDefinition;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use App\Models\ThirdPartyProviders;
use Faker\Factory as Faker;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Request;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/



//OAUTH

Route::get('/auth/google/registerationRedirect', [OauthController::class, 'registerationRedirect']);
Route::get('/auth/google/loginRedirect', [OauthController::class, 'loginRedirect']);
Route::get('/auth/google/callback', [OauthController::class, 'googleCallback']);

Route::get('/auth/facebook/redirect', [OauthController::class, 'facebookRedirect']);
Route::get('/auth/facebook/callback', [OauthController::class, 'facebookCallback']);

//OAUTH
Route::get('/', function () {
    //    $ip = Request:;
    //    dd(Request);
    return view('welcome');
});


Route::get('youtubeURL', function () {
    $url = 'https://www.youtube.com/watch?v=BJatgOiiht4';
    if (str_contains($url, 'youtube.com') || str_contains($url, 'youtu.be') || str_contains($url, 'm.youtube.com')) {
        parse_str(parse_url($url, PHP_URL_QUERY), $queryParams);
        $videoId = (is_array($queryParams) && isset($queryParams['v'])) ? $queryParams['v'] : null;
        if (!$videoId) {
            return null;
        }
        $thumbnailUrl = "https://img.youtube.com/vi/{$videoId}/hqdefault.jpg";
        // Fetch video details from oEmbed API
        $apiUrl = "https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v={$videoId}&format=json";
        try {
            $client = new \GuzzleHttp\Client();
            $response = $client->get($apiUrl);
            $videoData = json_decode($response->getBody(), true);
            return [
                'full response' => $videoData,
                'author_name' => $videoData['author_name'] ?? null, // YouTube oEmbed doesn't provide full description
                'thumbnail' => $thumbnailUrl,
            ];
        } catch (\Exception $e) {
            return null;
        }
    }
    return null;
});

Route::prefix('subscriptions')->group(function () {
    Route::get('/import-subscriptions', [SubscriptionController::class, 'importSubscriptions']);
});

Route::get('checkJson', function () {
    // Fetch all subscriptions with their related features
    $subscriptions = \App\Models\Subscription::with(['features'])->get();

    // Transform the data into the desired structure
    $formattedData = $subscriptions->map(function ($subscription) {
        return [
            'subscription' => $subscription->name,
            'features' => $subscription->features->map(function ($feature) {
                return [
                    'name' => $feature->name,
                    'limit' => $feature->pivot->feature_limit
                ];
            })
        ];
    });

    return response()->json($formattedData, 200, [], JSON_PRETTY_PRINT);
});


Route::get('trial/{id}/{trial}', [SubscriptionController::class ,'startFreeTrial']);
//Route::get('blog_sitemap',[\App\Http\Controllers\BlogController::class ,'blogs_sitemap']);



Route::get('/third-party-providers', function () {
    $data = [
        'name' => 'Interactive Brokers',
        'title' => 'Interactive Brokers',
        'API' => '1010',
        'Secret' => '1010',
        'Password' => '1010',
        'status' => '1',
        'installed' => '1',
        'activated' => '1',
        'URL' => 'https://www.interactivebrokers.com/',
        'product_id' => '1010',
        'Stocks' => '1',
        'crypto' => '0',
        'auto_sync' => '1',
    ];

    $provider = ThirdPartyProviders::create($data);
    return response()->json(['message' => 'Provider added successfully', 'data' => $provider], 201);
});



Route::get('/article/{type}', function ($type) {
    $faker = Faker::create();

    // Generate dummy data
    $data = [
        'title' => $faker->sentence,
        'content' => $faker->paragraphs(3, true),
        'feature_image' => $faker->imageUrl(800, 600, 'business'),
        'tags' => json_encode([$faker->word, $faker->word, $faker->word]),
        'primary_category_id' => rand(1, 10),
        'secondary_categories' => json_encode([rand(1, 10), rand(11, 20)]),
        'summary' => $faker->text(250),
    ];

    if ($type == 'blog') {
        $blog = Blog::create($data);
        return response()->json(['message' => 'Blog added successfully', 'data' => $blog], 201);
    } else {
        $education = Education::create($data);
        return response()->json(['message' => 'Education article added successfully', 'data' => $education], 201);
    }
});





Route::get('/create-admin', function () {
    // Static values for admin registration
    $name = 'Admin User';
    $email = '<EMAIL>';
    $password = 'Password123@'; // Change this as needed
    $username = 'adminTradeReply'; // Change this as needed

    // Check if the user already exists
    if (User::where('email', $email)->exists()) {
        return response()->json(['message' => 'Admin already exists!'], 400);
    }

    // Create the user
    $user = User::create([
        'name' => $name,
        'email' => $email,
        'password' => Hash::make($password),
        'username' => $username,
        'role' => 'Super admin'
    ]);


    return response()->json(['message' => 'Admin registered successfully!', 'user' => $user]);
});


Route::get('/clear-cache', function () {
    Artisan::call('cache:clear');

    Artisan::call('view:clear');

    Artisan::call('route:clear');

    Artisan::call('config:clear');

    Artisan::call('clear-compiled');

    // Optionally, you can add more cache-clearing commands here
    // Artisan::call('optimize:clear'); // Laravel 8.x and above

    return "All cache cleared successfully!";
});

Route::get('getbrokers', function () {
    $thirds = ThirdPartyProviders::all();
    return $thirds;
});

Route::get('importCSVBrokers', function () {
    // Delete existing brokers

    $thirds = ThirdPartyProviders::all();
    foreach ($thirds as $third) {
        $third->delete();
    }
    // Define the CSV file path
    $filename = 'brokers1.csv';
    $filePath = public_path('CSV/' . $filename);

    // Check if the file exists
    if (!file_exists($filePath)) {
        return response()->json(['error' => 'CSV file not found'], 404);
    }
    // Open the CSV file
    $file = fopen($filePath, 'r');
    if (!$file) {
        return response()->json(['error' => 'Failed to open CSV file'], 500);
    }

    // Read headers and clean them
    $headers = fgetcsv($file);

    // 🔥 Fix: Remove invisible BOM characters from first header
    $headers[0] = preg_replace('/\xEF\xBB\xBF/', '', $headers[0]);

    // Trim spaces to avoid hidden characters
    $headers = array_map(fn ($header) => trim($header), $headers);


    Log::info('Extracted Headers: ', ['headers' => $headers]); // Debugging

    $dataToInsert = [];

    // Read the CSV file row by row
    while (($row = fgetcsv($file)) !== false) {
        // Ensure row length matches header count
        if (count($row) !== count($headers)) {
            Log::error('CSV row mismatch', ['row' => $row]);
            continue;
        }

        // Combine headers with row data
        $data = array_combine($headers, $row);

        Log::info('Extracted Row:', $data); // Debugging

        // Prepare data for insertion
        $dataToInsert[] = [
            'name' => $data['title'] ?? 'Unknown', // Use 'Unknown' as a fallback if 'title' is not set
            'url' => $data['url'] ?? 'https://example.com',
            'stocks' => isset($data['stocks']) && strtolower(trim($data['stocks'])) === 'yes' ? 1 : 0,
            'crypto' => isset($data['crypto']) && strtolower(trim($data['crypto'])) === 'yes' ? 1 : 0,
            'auto_sync' => isset($data['autosync']) && strtolower(trim($data['autosync'])) === 'yes' ? 1 : 0,
            'title' => 'Dummy Title',
            'api' => 'Dummy API',
            'secret' => 'Dummy Secret',
            'password' => 'Dummy Password',
            'status' => 1,
            'installed' => 0,
            'activated' => 0,
            'product_id' => null,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    fclose($file);

    // Bulk insert for better performance
    if (!empty($dataToInsert)) {
        DB::table('third_party_providers')->insert($dataToInsert);
    }

    return response()->json(['success' => 'CSV data imported successfully']);
});



Route::get('importCSVEducations', function () {

    // Delete all pivot records for articles of type='education'
    $educationArticleIDs = Article::where('type', 'education')->pluck('id');
    DB::table('article_secondary_categories')
        ->whereIn('article_id', $educationArticleIDs)
        ->delete();

    // Delete the articles themselves
    Article::where('type', 'education')->delete();


    $educationFilePath = public_path('CSV/educationArticles.csv');
    $jsonPath = public_path('CSV/dbfieldjson.json');
    $termCategoriesFilePath = public_path('CSV/Term_Categories.csv');
    $imageMappingFilePath = public_path('CSV/Map-images-to-education-articles.csv');

    // Check if required files exist
    if (!file_exists($educationFilePath) || !file_exists($termCategoriesFilePath) || !file_exists($jsonPath) || !file_exists($imageMappingFilePath)) {
        return response()->json(['error' => 'One or more required files are missing'], 404);
    }

    // Load JSON data and map DATABASE FIELD to FIELD NAME and SUMMARY
    $jsonData = json_decode(file_get_contents($jsonPath), true);
    if (!is_array($jsonData)) {
        return response()->json(['error' => 'Invalid JSON data'], 400);
    }

    $fieldMappings = collect($jsonData)->mapWithKeys(fn ($item) => [
        trim($item['DATABASE FIELD']) => [
            'field_name' => trim($item['FIELD NAME'] ?? ''),
            'summary' => trim($item['SUMMARY'] ?? '')
        ]
    ]);

    // Read Term Categories CSV to map DATABASE FIELD to categories
    $file = fopen($termCategoriesFilePath, 'r');
    $termHeaders = fgetcsv($file);
    $termHeaders[0] = preg_replace('/\xEF\xBB\xBF/', '', $termHeaders[0]); // Remove BOM
    $termHeaders = array_map('trim', $termHeaders);

    $categoryMapping = [];
    while (($row = fgetcsv($file)) !== false) {
        if (count($row) !== count($termHeaders)) {
            Log::error('CSV row mismatch in term_categories.csv', ['row' => $row]);
            continue;
        }
        $data = array_combine($termHeaders, $row);
        $databaseField = trim($data['DATABASE FIELD']);
        $primaryCategory = trim($data['PRIMARY CATEGORY']);
        $otherCategories = array_map('trim', array_filter(array_slice($data, 2)));

        $categoryMapping[$databaseField] = [
            'primary' => $primaryCategory,
            'categories' => array_values($otherCategories),
        ];
    }
    fclose($file);

    // Fetch category IDs
    $categoryData = Category::pluck('id', 'database_field')->toArray();

    // Read Image Mapping CSV
    $imageMapping = [];
    $file = fopen($imageMappingFilePath, 'r');

    $headers = fgetcsv($file);
    if ($headers) {
        $headers[0] = preg_replace('/\xEF\xBB\xBF/', '', $headers[0]); // Remove BOM
        $headers = array_map('trim', $headers);
    }

    Log::info('Extracted headers from image mapping CSV:', $headers);

    if (!$headers || !in_array('filename', $headers) || !in_array('article_database_field', $headers)) {
        return response()->json([
            'error' => 'Invalid headers in image mapping CSV',
            'headers' => $headers // Return headers for debugging
        ], 400);
    }

    while (($row = fgetcsv($file)) !== false) {
        if (count($row) !== count($headers)) {
            Log::error('CSV row mismatch in Map-images-to-education-articles.csv', ['row' => $row]);
            continue;
        }

        $data = array_combine($headers, $row);

        if (!isset($data['filename']) || !isset($data['article_database_field'])) {
            Log::error('Missing keys in image mapping row', ['row' => $row]);
            continue;
        }

        // Trim values and store in array
        $imageMapping[trim($data['article_database_field'])] = pathinfo(trim($data['filename']), PATHINFO_BASENAME);
    }

    fclose($file);

    // Read and process educationArticles.csv
    $educationFile = fopen($educationFilePath, 'r');
    $eduHeaders = fgetcsv($educationFile);
    $eduHeaders[0] = preg_replace('/\xEF\xBB\xBF/', '', $eduHeaders[0]); // Remove BOM
    $eduHeaders = array_map('trim', $eduHeaders);

    Log::info('Extracted Headers from educationArticles.csv', ['headers' => $eduHeaders]);

    // Clear previous education articles (ONLY IF REQUIRED)
    Article::where('type', 'education')->delete();

    $pivotToInsert = [];

    while (($row = fgetcsv($educationFile)) !== false) {
        if (count($row) !== count($eduHeaders)) {
            Log::error('CSV row mismatch in educationArticles.csv', ['row' => $row]);
            continue;
        }

        $data = array_combine($eduHeaders, $row);
        $databaseField = trim($data['title']);

        if (!isset($fieldMappings[$databaseField])) {
            Log::warning("No field mapping found for: $databaseField");
            continue;
        }

        $fieldData = $fieldMappings[$databaseField];
        $title = $fieldData['field_name'];
        $summary = $fieldData['summary'];
        $content = trim($data['content']);

        if (!isset($categoryMapping[$databaseField])) {
            Log::warning("No category mapping found for: $databaseField");
            continue;
        }

        $categoriesData = $categoryMapping[$databaseField];
        $primaryCategory = $categoriesData['primary'];
        $secondaryCategories = $categoriesData['categories'];

        $primaryCategoryId = $categoryData[$primaryCategory] ?? null;
        // inside the loop where you're dealing with $primaryCategoryId
        $primaryCategoryId = $categoryData[$primaryCategory] ?? null;

        if (is_null($primaryCategoryId)) {
            Log::warning("[Education CSV Import] Primary Category not found in categoryData", [
                'databaseField' => $databaseField,
                'primaryCategory' => $primaryCategory,
                'availableCategoryKeys' => array_keys($categoryData),
            ]);
        }
        $secondaryCategoryIds = array_filter(array_map(fn ($cat) => $categoryData[$cat] ?? null, $secondaryCategories));

        $featureImage = $imageMapping[$databaseField] ?? null;

        $slug = Article::generateUniqueSlug($title, 'education'); // Generate slug first

        // ✅ Use insertGetId() to retrieve the ID
        $articleId = DB::table('articles')->insertGetId([
            'title' => $title,
            'content' => $content,
            'summary' => $summary,
            'primary_category_id' => $primaryCategoryId,
            'feature_image' => $featureImage,
            'slug' => $slug,
            'is_featured' => 0,
            'type' => 'education',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // ✅ Insert secondary categories into the pivot table
        foreach ($secondaryCategoryIds as $categoryId) {
            $pivotToInsert[] = [
                'article_id' => $articleId,
                'category_id' => $categoryId,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
    }
    fclose($educationFile);

    if (!empty($pivotToInsert)) {
        DB::table('article_secondary_categories')->insert($pivotToInsert);
    }

    return response()->json(['success' => 'CSV data imported successfully']);
});



Route::get('/run-migration', function () {
    Artisan::call('migrate');
    return response()->json(['success' => 'Migrations executed successfully']);
});

Route::get('/clear-cache', function () {
    Artisan::call('cache:clear');
    Artisan::call('config:clear');
    Artisan::call('route:clear');
    Artisan::call('view:clear');
    Artisan::call('optimize:clear');

    return 'All caches cleared!';
});



Route::get('/import-field-definitions', function () {
    // Adjust the file path as needed
    $jsonData = file_get_contents(public_path('CSV/dbfieldjson.json'));
    //    dd($jsonData);
    $fields = json_decode($jsonData, true);
    //dd($fields);
    if (!is_array($fields)) {
        return response()->json(['error' => 'Invalid JSON data provided.'], 400);
    }

    foreach ($fields as $field) {
        // Determine metric_dimension: if "DIMENSION" is YES, use 'dimension'; if "METRIC" is YES, use 'metric'
        $metricDimension = (isset($field['DIMENSION']) && strtoupper($field['DIMENSION']) === 'YES')
            ? 'dimension'
            : ((isset($field['METRIC']) && strtoupper($field['METRIC']) === 'YES') ? 'metric' : null);

        // Convert "HAS FORMULA" string to boolean.
        $hasFormula = (isset($field['HAS FORMULA']) && strtoupper($field['HAS FORMULA']) === 'YES');

        // Create the main field definition record.
        $fieldDefinition = FieldDefinition::create([
            'field_name'      => $field['FIELD NAME'] ?? null,
            'database_field'  => $field['DATABASE FIELD'] ?? null,
            'summary'         => $field['SUMMARY'] ?? null,
            'datatype'        => $field['DATATYPE'] ?? null,
            'expected_values' => $field['EXPECTED VALUES'] ?? null,
            'has_formula'     => $hasFormula,
            'metric_dimension' => $metricDimension,
        ]);

        // Insert Transaction scope details if provided.
        if (isset($field['SCOPES']['TRANSACTION'])) {
            $transactionScope = $field['SCOPES']['TRANSACTION'];
            TransactionFieldDefinition::create([
                'field_definition_id' => $fieldDefinition->id,
                'database_field'      => $transactionScope['DATABASE FIELD'] ?? null,
                'summary'             => $transactionScope['SUMMARY'] ?? null,
                'account_field'       => $transactionScope['ACCOUNT FIELD'] ?? null,
            ]);
        }

        // Insert Trade scope details if provided.
        if (isset($field['SCOPES']['TRADE'])) {
            $tradeScope = $field['SCOPES']['TRADE'];
            TradeFieldDefinition::create([
                'field_definition_id' => $fieldDefinition->id,
                'database_field'      => $tradeScope['DATABASE FIELD'] ?? null,
                'summary'             => $tradeScope['SUMMARY'] ?? null,
                'account_field'       => $tradeScope['ACCOUNT FIELD'] ?? null,
            ]);
        }

        // Insert Portfolio scope details if provided.
        if (isset($field['SCOPES']['PORTFOLIO'])) {
            $portfolioScope = $field['SCOPES']['PORTFOLIO'];
            PortfolioFieldDefinition::create([
                'field_definition_id' => $fieldDefinition->id,
                'database_field'      => $portfolioScope['DATABASE FIELD'] ?? null,
                'summary'             => $portfolioScope['SUMMARY'] ?? null,
                'account_field'       => $portfolioScope['ACCOUNT FIELD'] ?? null,
            ]);
        }
    }

    return response()->json(['message' => 'Field definitions imported successfully.']);
});



Route::get('/import-categories', function () {
    $filePath = public_path('CSV/TradeReply-Categories.csv');

    $fetchs = Category::all();
    foreach ($fetchs as $fetch) {
        $fetch->delete();
    }
    // 1. Check if CSV file exists.
    if (! file_exists($filePath)) {
        return response()->json(['error' => 'CSV file not found'], 404);
    }

    // 2. Attempt to open the file.
    if (($handle = fopen($filePath, 'r')) === false) {
        return response()->json(['error' => 'Unable to open CSV file'], 500);
    }

    // 3. Read the header row.
    $headers = fgetcsv($handle);

    // Remove any BOM from the first header cell (in case the CSV has a BOM).
    if ($headers && isset($headers[0])) {
        $headers[0] = preg_replace('/^\xEF\xBB\xBF/', '', $headers[0]);
    }

    // If no headers are found, close the file and return an error.
    if (! $headers) {
        fclose($handle);
        return response()->json(['error' => 'CSV file is empty or invalid'], 400);
    }

    // 4. Loop through each subsequent row in the CSV.
    while (($row = fgetcsv($handle)) !== false) {
        // Combine the headers with the row values into an associative array.
        $data = array_combine($headers, $row);

        // Retrieve the CSV values. Adjust keys if your CSV headers differ.
        $title   = $data['CATEGORY']          ?? null;
        $content = $data['SUMMARY']           ?? null;
        $slug    = $data['SLUG']              ?? null;
        $dbField = $data['DATABASE FIELD']    ?? null;

        Log::info('db field', [$dbField]);
        // 6. Create the Category record. If you need to check for duplicates or update existing records,
        //    you would add that logic here.
        Category::create([
            'title'          => $title,
            'content'        => $content,
            'slug'           => $slug,
            'database_field' => $dbField,
        ]);
    }

    // Close the file when done.
    fclose($handle);

    return response()->json(['message' => 'Categories imported successfully']);
});



Route::post('/education-images', function () {

});

Route::get('/test', function () {
    //dd(Storage::disk('s3')->url'($this->feature_image'))
    //    $blogs = Blog::with(['primaryCategory:id,title'])
    //        ->paginate(10);
    ////    dd($blogs);
    //    return response()->json([
    //        'success' => true,
    //        'message' => 'Blogs List',
    //        'data' => BlogResource::collection($blogs),
    //        'pagination' => [
    //            'total' => $blogs->total(),
    //            'count' => $blogs->count(),
    //            'per_page' => $blogs->perPage(),
    //            'current_page' => $blogs->currentPage(),
    //            'total_pages' => $blogs->lastPage(),
    //            'next_page_url' => $blogs->nextPageUrl(),
    //            'prev_page_url' => $blogs->previousPageUrl(),
    //        ],
    //    ]);
});


Route::get('/update-product-category', function () {
    $json = file_get_contents(public_path('CSV/marketplace-category.json')); // or paste your json string here
    $data = json_decode($json, true);

    DB::transaction(function () use ($data) {
        foreach ($data['categories'] as $category) {
            $main = DB::table('product_categories')->updateOrInsert(
                ['slug' => $category['slug']],
                ['name' => $category['name'], 'sort_order' => 0, 'created_at' => now(), 'updated_at' => now()]
            );

            // Get ID of the main category
            $mainCategoryId = DB::table('product_categories')->where('slug', $category['slug'])->value('id');

            foreach ($category['subcategories'] as $subcategory) {
                $sub = DB::table('product_subcategories')->updateOrInsert(
                    ['slug' => $subcategory['slug']],
                    ['name' => $subcategory['name'], 'product_category_id' => $mainCategoryId, 'sort_order' => 0, 'created_at' => now(), 'updated_at' => now()]
                );

                // Get ID of the sub category
                $subCategoryId = DB::table('product_subcategories')->where('slug', $subcategory['slug'])->value('id');

                if (!empty($subcategory['subcategories'])) {
                    foreach ($subcategory['subcategories'] as $child) {
                        DB::table('product_child_subcategories')->updateOrInsert(
                            ['slug' => $child['slug']],
                            ['name' => $child['name'], 'product_subcategory_id' => $subCategoryId, 'sort_order' => 0, 'created_at' => now(), 'updated_at' => now()]
                        );
                    }
                }
            }
        }
    });

    return 'Product categories updated successfully.';
});

Route::post('/stripe/webhook', [PlanController::class, 'handleWebhook']);
