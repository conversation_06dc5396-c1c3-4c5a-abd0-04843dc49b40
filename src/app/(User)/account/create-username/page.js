'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import TextInput from "@/Components/UI/TextInput";
import "@/css/account/AccountDetails.scss";

export default function CreateUsername() {
    const [username, setUsername] = useState('');

    const metaArray = {
        noindex: true,
        title: "Create Username | TradeReply",
        description: "Create or update your username on TradeReply.com.",
        canonical_link: "https://www.tradereply.com/account/create-username",
        og_site_name: "TradeReply",
        og_title: "Create Username | TradeReply",
        og_description: "Create or update your username on TradeReply.com.",
        twitter_title: "Create Username | TradeReply",
        twitter_description: "Create or update your username on TradeReply.com.",
    };

    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_create_username">
                    <SidebarHeading title="Create Username" />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <h6>Username</h6>
                                    <p>Choose a unique username for your TradeReply account. This will be your public identifier and can be changed up to 2 times.</p>
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <div className="account_card_list">
                                    <div className="col-lg-5 col-md-8 col-12">
                                        <TextInput
                                            type="text"
                                            placeholder="Enter your username"
                                            value={username}
                                            onChange={(e) => setUsername(e.target.value)}
                                        />
                                    </div>
                                    <div className="col-12 mt-3">
                                        <div className="username-rules">
                                            <h6>Username Requirements:</h6>
                                            <ul>
                                                <li>Must be between 3-20 characters</li>
                                                <li>Can contain letters, numbers, and underscores</li>
                                                <li>Must start with a letter</li>
                                                <li>Cannot contain spaces or special characters</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="account_card_list_btns mt-3">
                            <button className="btn-style white-btn">
                                Cancel
                            </button>
                            <button className="btn-style">
                                Save Username
                            </button>
                        </div>
                    </div>
                </div>
            </AccountLayout>
        </>
    )
}
