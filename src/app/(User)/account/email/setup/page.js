'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import TextInput from "@/Components/UI/TextInput";
import "@/css/account/AccountDetails.scss";

export default function SetupEmail() {

    const metaArray = {
        noindex: true,
        title: "Setup Email Address | Update Info | TradeReply",
        description: "Update your email address on TradeReply.com.",
        canonical_link: "https://www.tradereply.com/account/details",
        og_site_name: "TradeReply",
        og_title: "Setup Email Address | Update Info | TradeReply",
        og_description: "Update your email address on TradeReply.com.",
        twitter_title: "Setup Email Address | Update Info | TradeReply",
        twitter_description: "Update your email address on TradeReply.com.",
    };
    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_setup_email">
                    <SidebarHeading title="New Email Address" />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <h6>Email</h6>
                                    <p>Enter the email address you want associated with your TradeReply account. You will use this email address to log in.</p>
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <div className="account_card_list">
                                    <div className="col-lg-5 col-md-8 col-12">
                                        <TextInput
                                            type="text"
                                            placeholder="Enter your email address"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="account_card_list_btns mt-3">
                            <button className="btn-style white-btn" >
                                Cancel
                            </button>
                            <button className="btn-style" >
                                Save
                            </button>
                        </div>
                    </div>

                </div>
            </AccountLayout>
        </>
    )
}
