'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";
import { CheckIcon, PlusIconSvg, RightArrowIconSvg } from "@/assets/svgIcons/SvgIcon";
import CircularProgressbarWithChildren from "@/Components/graph/CircularProgressbarWithChildren";
import { get } from "@/utils/apiUtils";
import { setUser } from "@/redux/authSlice";
import Link from "next/link";

export default function SecurityCheckup() {
    const [userData, setUserData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    const dispatch = useDispatch();
    const reduxUser = useSelector((state) => state?.auth?.user || null);

    // Fetch user data from API
    const fetchUserData = async () => {
        try {
            setLoading(true);
            setError(null);

            const response = await get('/account');

            if (response.success && response.data) {
                setUserData(response.data);
                // Update Redux store with fresh user data
                dispatch(setUser(response.data));
                // Also update localStorage to ensure consistency
                localStorage.setItem('user', JSON.stringify(response.data));
            } else {
                throw new Error(response.message || 'Failed to fetch user data');
            }
        } catch (err) {
            console.error('SecurityCheckup: Error fetching user data:', err);
            setError(err.message || 'Failed to load user information');

            // Fallback to Redux user data if API fails
            if (reduxUser) {
                setUserData(reduxUser);
            }
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        // Always fetch fresh data first to ensure we have the latest from DB
        fetchUserData();

        // Also check for cached data as fallback
        const storedUser = localStorage.getItem('user');

        if (reduxUser) {
            // Use Redux data if available, but still fetch fresh data
            setUserData(reduxUser);
            setLoading(false);
        } else if (storedUser) {
            // Use localStorage data as immediate fallback while API loads
            try {
                const parsedUser = JSON.parse(storedUser);
                setUserData(parsedUser);
                // Update Redux store
                dispatch(setUser(parsedUser));
                setLoading(false);
            } catch (err) {
                console.error('SecurityCheckup: Error parsing stored user data:', err);
            }
        }
    }, []); // Empty dependency array to run only on mount

    // Calculate security status
    const getSecurityStatus = () => {
        if (!userData) {
            return {
                emailVerified: false,
                twoFactorEnabled: false,
                phoneNumberAdded: false,
                completionPercentage: 0
            };
        }

        // Email verification - always true as per requirements
        const emailVerified = true; // Users must verify email during account creation

        // 2FA status
        const twoFactorEnabled = userData.is_two_factor_enabled || userData.two_factor_enabled || false;

        // Phone number status
        const phoneNumberAdded = userData.has_phone_number || (userData.phone_number && userData.phone_number.trim() !== '') || false;

        // Calculate completion percentage (3 total items)
        let completedItems = 0;
        if (emailVerified) completedItems++;
        if (twoFactorEnabled) completedItems++;
        if (phoneNumberAdded) completedItems++;

        const completionPercentage = Math.round((completedItems / 3) * 100);

        return {
            emailVerified,
            twoFactorEnabled,
            phoneNumberAdded,
            completionPercentage,
            completedItems,
            totalItems: 3
        };
    };

    const securityStatus = getSecurityStatus();

    // Handle click for 2FA setup
    const handle2FAClick = () => {
        // Direct link to 2FA setup - middleware will handle security verification
        window.location.href = '/account/2fa/setup';
    };

    // Handle click for phone number setup
    const handlePhoneClick = () => {
        // Direct link to phone setup - middleware will handle security verification
        window.location.href = '/account/phone/setup';
    };

    return (
        <>
            <Col md={6} xs={12} className="mb-3 mb-lg-4">
                <CommonBlackCard
                    link="/account/security"
                    title="Security Checkup"
                    Linktext="Security"
                    Linkicon={<RightArrowIconSvg />}
                    className="account_card"
                >
                    <div className="account_card_checkup d-flex">
                        <div className="account_card_checkup_verify">
                            {loading ? (
                                <div className="text-center py-3">
                                    <span>Loading security status...</span>
                                </div>
                            ) : error ? (
                                <div className="text-center py-3">
                                    <span className="text-danger">Failed to load security status</span>
                                    <br />
                                    <button
                                        className="btn btn-sm btn-link"
                                        onClick={fetchUserData}
                                        style={{ color: '#007bff', textDecoration: 'underline' }}
                                    >
                                        Retry
                                    </button>
                                </div>
                            ) : (
                                <>
                                    {/* Email Verification - Always completed */}
                                    <div className="mb-3 d-flex align-items-center">
                                        <CheckIcon />
                                        <h6 className="ps-4">Email Verified</h6>
                                    </div>

                                    {/* 2-Step Verification */}
                                    <div className="mb-3 d-flex align-items-center">
                                        {securityStatus.twoFactorEnabled ? (
                                            <>
                                                <CheckIcon />
                                                <h6 className="ps-4">2-Step Verification</h6>
                                            </>
                                        ) : (
                                            <button
                                                className="add_number d-flex align-items-center"
                                                type="button"
                                                onClick={handle2FAClick}
                                            >
                                                <PlusIconSvg />
                                                <span>Enable 2-Step Verification</span>
                                            </button>
                                        )}
                                    </div>

                                    {/* Phone Number */}
                                    <div className="d-flex align-items-center">
                                        {securityStatus.phoneNumberAdded ? (
                                            <>
                                                <CheckIcon />
                                                <h6 className="ps-4">Phone Number</h6>
                                            </>
                                        ) : (
                                            <button
                                                className="add_number d-flex align-items-center"
                                                type="button"
                                                onClick={handlePhoneClick}
                                            >
                                                <PlusIconSvg /> Add phone number
                                            </button>
                                        )}
                                    </div>
                                </>
                            )}
                        </div>
                        <div className="account_card_checkup_chart">
                            <CircularProgressbarWithChildren value={securityStatus.completionPercentage}>
                                <div className="CircularProgressbar_text">
                                    <h6>{securityStatus.completionPercentage}%</h6>
                                    <h6 className="text-uppercase">Complete</h6>
                                </div>
                            </CircularProgressbarWithChildren>
                        </div>
                    </div>
                </CommonBlackCard>
            </Col>
        </>
    );
}
