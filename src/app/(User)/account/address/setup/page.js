'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import TextInput from "@/Components/UI/TextInput";
import "@/css/account/AccountDetails.scss";
import CustomDropdown from "@/Components/common/CustumDropdown";
import countries from 'world-countries';

export default function SetupNewAddress() {
    const [country, setCountry] = useState('');
    const [selectedCountry, setSelectedCountry] = useState(country);

    const selectCountry = (country) => {
        console.log('Selected:', country);
        setSelectedCountry(country.name.common);
    };
    const metaArray = {
        noindex: true,
        title: "Add New Address | Update Info | TradeReply",
        description: "Add new address on TradeReply.com.",
        canonical_link: "https://www.tradereply.com/account/details",
        og_site_name: "TradeReply",
        og_title: "Add New Address | Update Info | TradeReply",
        og_description: "Add new address on TradeReply.com.",
        twitter_title: "Add New Address | Update Info | TradeReply",
        twitter_description: "Add new address on TradeReply.com.",
    };
    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_setup_email">
                    <SidebarHeading title="Add New Address" />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <h6>New Address</h6>
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <div className="account_card_list row col-5">
                                    <Col xs={12} className="mb-4">
                                        <CustomDropdown
                                            options={countries.map((c) => ({ label: c.name.common, ...c }))}
                                            defaultValue="Select Country"
                                            onSelect={selectCountry}
                                        />
                                    </Col>
                                    <Col xs={6}>
                                        <TextInput
                                            type="text"
                                            placeholder="First Name"
                                        />
                                    </Col>
                                    <Col xs={6}>
                                        <TextInput
                                            type="text"
                                            placeholder="Last Name"
                                        />
                                    </Col>
                                    <Col xs={12}>
                                        <TextInput
                                            type="text"
                                            placeholder="Address"
                                        />
                                    </Col>
                                    <Col xs={12}>
                                        <TextInput
                                            type="text"
                                            placeholder="Address Line 2 (Optional)"
                                        />
                                    </Col>
                                    <Col xs={12}>
                                        <TextInput
                                            type="text"
                                            placeholder="City"
                                        />
                                    </Col>
                                    <Col xs={12}>
                                        <TextInput
                                            type="text"
                                            placeholder="State or Region"
                                        />
                                    </Col>
                                    <Col xs={12}>
                                        <TextInput
                                            type="text"
                                            placeholder="Zip or Postal Code"
                                        />
                                    </Col>
                                </div>
                            </div>
                        </div>
                        <div className="account_card_list_btns mt-3">
                            <button className="btn-style white-btn" >
                                Cancel
                            </button>
                            <button className="btn-style" >
                                Save
                            </button>
                        </div>
                    </div>

                </div>
            </AccountLayout>
        </>
    )
}
