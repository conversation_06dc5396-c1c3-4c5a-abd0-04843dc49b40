'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import TextInput from "@/Components/UI/TextInput";
import "@/css/account/AccountDetails.scss";
import CustomDropdown from "@/Components/common/CustumDropdown";
import countries from 'world-countries';
import Link from "next/link";
import { EditIconSvg, PlusIconSvg, CheckIcon, RemoveIconSvg } from '@/assets/svgIcons/SvgIcon';

export default function SetupNewAddress() {
    const [country, setCountry] = useState('');
    const [selectedCountry, setSelectedCountry] = useState(country);

    const selectCountry = (country) => {
        console.log('Selected:', country);
        setSelectedCountry(country.name.common);
    };

    const [editingAddressId, setEditingAddressId] = useState(null);
    const [editFormData, setEditFormData] = useState({
        firstName: '',
        lastName: '',
        address: '',
        city: '',
        state: '',
        zip: '',
        isDefault: false
    });

    const handleUpdateAddress = () => {
        setAddresses((prev) =>
            prev.map((addr) =>
                addr.id === editingAddressId
                    ? {
                        ...editFormData,
                        id: editingAddressId,
                        isDefault: editFormData.isDefault,
                    }
                    : {
                        ...addr,
                        isDefault: editFormData.isDefault ? false : addr.isDefault,
                    }
            )
        );
        setEditingAddressId(null);
    };


    const [addresses, setAddresses] = useState([
        {
            id: 1,
            firstName: 'Aaron',
            lastName: 'McCloud',
            address: '245434534543543543',
            city: 'Sandford',
            state: 'FL',
            zip: '32771',
            isDefault: true,
        },
        {
            id: 2,
            firstName: 'Aaron',
            lastName: 'McCloud',
            address: '73525434534543543543',
            city: 'Sandford',
            state: 'FL',
            zip: '32771',
            isDefault: false,
        },
    ]);

    const [showConfirm, setShowConfirm] = useState(null);

    const maskName = (name) => {
        const parts = name.split(' ');
        return parts
            .map((part) => {
                if (part.length <= 2) return part[0] + '*';
                return part[0] + '*'.repeat(part.length - 2);
            })
            .join(' ');
    };

    const maskAddress = (address) => {
        if (!address) return '';
        return address[0] + '*'.repeat(address.length - 1);
    };

    const handleDelete = (id) => {
        setAddresses(prev => prev.filter(addr => addr.id !== id));
        setShowConfirm(null);
    };
    const metaArray = {
        noindex: true,
        title: "Manage Your Addresses | Update Info | TradeReply",
        description: "Manage your address on TradeReply.com.",
        canonical_link: "https://www.tradereply.com/account/details",
        og_site_name: "TradeReply",
        og_title: "Manage Your Addresses | Update Info | TradeReply",
        og_description: "Manage your address on TradeReply.com.",
        twitter_title: "Manage Your Addresses | Update Info | TradeReply",
        twitter_description: "Manage your address on TradeReply.com.",
    };
    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_setup_email">
                    <SidebarHeading title="Manage Your Addresses" />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <h6>Address Book</h6>
                                </div>
                                <div className="common_blackcard_innerheader_icon">
                                    <Link href="/account/address/setup" prefetch={true}>
                                        <button className="d-flex align-items-center">
                                            <PlusIconSvg />
                                            <span className="ms-2">Add New Address</span>
                                        </button>
                                    </Link>
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <div className="account_card_list new-address-section">
                                    <ul>
                                        {[...addresses]
                                            .sort((a, b) => (b.isDefault === true) - (a.isDefault === true))
                                            .map((addr) => (
                                                <React.Fragment key={addr.id}>
                                                    <li
                                                        className={editingAddressId === addr.id ? 'border-bottom-none' : ''}
                                                    >
                                                        <Col xs={12} md={3}>
                                                            {addr.isDefault ? (
                                                                <div className="d-flex align-items-center gap-2">
                                                                    <CheckIcon />
                                                                    <span className="green_text">Default</span>
                                                                </div>
                                                            ) : (
                                                                <button>
                                                                    <span className='text_00ADEF'>Set as Default</span>
                                                                </button>
                                                            )}
                                                        </Col>
                                                        <Col xs={12} md={9}>
                                                            <div className='d-flex justify-content-between align-items-center w-100'>
                                                                <div className='show-address-details'>
                                                                    <p className='name'>{maskName(addr.firstName)}{maskName(addr.lastName)}</p>
                                                                    <p className='address'>{maskAddress(addr.address)}</p>
                                                                    <p className='city'>{addr.city}, {addr.state}, {addr.zip}</p>

                                                                    {showConfirm === addr.id && (
                                                                        <div className='remove-address-confirmation mt-2'>
                                                                            <p>Are you sure you want to remove this address?</p>
                                                                            <div className="btns d-flex gap-2">
                                                                                <button className="btn-style gray-btn" onClick={() => setShowConfirm(null)}>
                                                                                    Cancel
                                                                                </button>
                                                                                <button className="btn-style red-btn" onClick={() => handleDelete(addr.id)}>
                                                                                    Remove
                                                                                </button>
                                                                            </div>
                                                                        </div>
                                                                    )}
                                                                </div>

                                                                {editingAddressId !== addr.id && (
                                                                    <div className='btns d-flex gap-4'>
                                                                        {!addr.isDefault && showConfirm !== addr.id && (
                                                                            <button className="d-flex align-items-center" onClick={() => setShowConfirm(addr.id)}>
                                                                                <RemoveIconSvg />
                                                                                <span className="ms-1">Remove</span>
                                                                            </button>
                                                                        )}
                                                                        <button
                                                                            className="d-flex align-items-center"
                                                                            onClick={() => {
                                                                                setEditingAddressId(addr.id);
                                                                                setEditFormData({ ...addr });
                                                                            }}
                                                                        >
                                                                            <EditIconSvg />
                                                                            <span className="ms-1">Edit</span>
                                                                        </button>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </Col>
                                                    </li>

                                                    {editingAddressId === addr.id && (
                                                        <li>
                                                            <div className="row">
                                                                <Col xs={12} md={3} />
                                                                <Col xs={12} md={5}>
                                                                    <div className="row">
                                                                        <Col xs={12} className="mb-4">
                                                                            <CustomDropdown
                                                                                options={countries.map((c) => ({ label: c.name.common, ...c }))}
                                                                                defaultValue={editFormData.country || "Select Country"}
                                                                                onSelect={(c) => setEditFormData({ ...editFormData, country: c.name.common })}
                                                                            />
                                                                        </Col>
                                                                        <Col xs={6}>
                                                                            <TextInput
                                                                                type="text"
                                                                                placeholder="First Name"
                                                                                value={editFormData.firstName}
                                                                                onChange={(e) => setEditFormData({ ...editFormData, firstName: e.target.value })}
                                                                            />
                                                                        </Col>
                                                                        <Col xs={6}>
                                                                            <TextInput
                                                                                type="text"
                                                                                placeholder="Last Name"
                                                                                value={editFormData.lastName}
                                                                                onChange={(e) => setEditFormData({ ...editFormData, lastName: e.target.value })}
                                                                            />
                                                                        </Col>
                                                                        <Col xs={12}>
                                                                            <TextInput
                                                                                type="text"
                                                                                placeholder="Address"
                                                                                value={editFormData.address}
                                                                                onChange={(e) => setEditFormData({ ...editFormData, address: e.target.value })}
                                                                            />
                                                                        </Col>
                                                                        <Col xs={12}>
                                                                            <TextInput
                                                                                type="text"
                                                                                placeholder="City"
                                                                                value={editFormData.city}
                                                                                onChange={(e) => setEditFormData({ ...editFormData, city: e.target.value })}
                                                                            />
                                                                        </Col>
                                                                        <Col xs={12}>
                                                                            <TextInput
                                                                                type="text"
                                                                                placeholder="State"
                                                                                value={editFormData.state}
                                                                                onChange={(e) => setEditFormData({ ...editFormData, state: e.target.value })}
                                                                            />
                                                                        </Col>
                                                                        <Col xs={12}>
                                                                            <TextInput
                                                                                type="text"
                                                                                placeholder="Zip"
                                                                                value={editFormData.zip}
                                                                                onChange={(e) => setEditFormData({ ...editFormData, zip: e.target.value })}
                                                                            />
                                                                        </Col>
                                                                        <div className="custom_checkbox my-2">
                                                                            <input
                                                                                className="custom_checkbox_input form-check-input"
                                                                                type="checkbox"
                                                                                id="defaultAddress"
                                                                                checked={editFormData.isDefault}
                                                                                onChange={(e) =>
                                                                                    setEditFormData({
                                                                                        ...editFormData,
                                                                                        isDefault: e.target.checked,
                                                                                    })
                                                                                }
                                                                            />
                                                                            <label className="custom_checkbox_label" htmlFor="defaultAddress">
                                                                                Set as default address
                                                                            </label>
                                                                        </div>
                                                                        <div className="account_card_list_btns">
                                                                            <button
                                                                                className="btn-style white-btn"
                                                                                onClick={() => setEditingAddressId(null)}
                                                                            >
                                                                                Cancel
                                                                            </button>
                                                                            <button
                                                                                className="btn-style"
                                                                                onClick={() => handleUpdateAddress()}
                                                                            >
                                                                                Save
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </Col>
                                                            </div>
                                                        </li>
                                                    )}
                                                </React.Fragment>
                                            ))}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </AccountLayout>
        </>
    )
}
