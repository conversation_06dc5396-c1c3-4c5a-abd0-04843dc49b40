'use client';
import React, { useState } from 'react';
import { Col } from 'react-bootstrap';
import { EditIconSvg, PlusIconSvg, CheckIcon, RemoveIconSvg } from '@/assets/svgIcons/SvgIcon';
import Link from 'next/link';

export default function AddressBook() {
    const addresses = [
        {
            id: 1,
            name: '<PERSON>',
            address: '245434534543543543',
            city: 'Sandford',
            state: 'FL',
            zip: '32771',
            isDefault: true,
        },
        {
            id: 2,
            name: '<PERSON>',
            address: '73525434534543543543',
            city: 'Sandford',
            state: 'FL',
            zip: '32771',
            isDefault: false,
        },
    ];

    const maskName = (name) => {
        const parts = name.split(' ');
        return parts
            .map((part) => {
                if (part.length <= 2) return part[0] + '*';
                return part[0] + '*'.repeat(part.length - 2);
            })
            .join(' ');
    };

    const maskAddress = (address) => {
        if (!address) return '';
        return address[0] + '*'.repeat(address.length - 1);
    };

    return (
        <>
            <Col lg={12} xs={12} className="mb-3 mb-lg-4">
                <div className="common_blackcard account_card">
                    <div className="common_blackcard_innerheader">
                        <div className="common_blackcard_innerheader_content">
                            <h6>Address Book</h6>
                        </div>
                        <div className="common_blackcard_innerheader_icon">
                            <Link href="/account/address/setup" prefetch={true}>
                                <button className="d-flex align-items-center">
                                    <PlusIconSvg />
                                    <span className="ms-2">Add New Address</span>
                                </button>
                            </Link>
                        </div>
                    </div>

                    <div className="common_blackcard_innerbody">
                        <div className="account_card_list new-address-section">
                            <ul>
                                {[...addresses]
                                    .sort((a, b) => (b.isDefault === true) - (a.isDefault === true))
                                    .map((addr) => (
                                        <li key={addr.id}>
                                            <Col xs={12} md={3}>
                                                {addr.isDefault ? (
                                                    <div className="d-flex align-items-center gap-2">
                                                        <CheckIcon />
                                                        <span className="green_text">Default</span>
                                                    </div>
                                                ) : (
                                                    <Link href="/security-check" prefetch={true}>
                                                        <button>
                                                            <span className='text_00ADEF'>Set as Default</span>
                                                        </button>
                                                    </Link>
                                                )}

                                            </Col>
                                            <Col xs={12} md={9}>
                                                <div className='d-flex justify-content-between align-items-center w-100'>
                                                    <div className='show-address-details'>
                                                        <p className='name'>{maskName(addr.name)}</p>
                                                        <p className='address'>{maskAddress(addr.address)}</p>
                                                        <p className='city'>{addr.city}, {addr.state}, {addr.zip}</p>
                                                    </div>

                                                    <div className='btns d-flex gap-2'>
                                                        {!addr.isDefault && (
                                                            <>
                                                                <Link href="/account/address/manage" prefetch={true}>
                                                                    <button className="d-flex align-items-center">
                                                                        <RemoveIconSvg />
                                                                        <span className="ms-1">Remove</span>
                                                                    </button>
                                                                </Link>
                                                                <Link href="/account/address/manage" prefetch={true}>
                                                                    <button className="d-flex align-items-center">
                                                                        <EditIconSvg />
                                                                        <span className="ms-1">Edit</span>
                                                                    </button>
                                                                </Link>
                                                            </>
                                                        )}

                                                        {addr.isDefault && (
                                                            <Link href="/account/address/manage" prefetch={true}>
                                                                <button className="d-flex align-items-center">
                                                                    <EditIconSvg />
                                                                    <span className="ms-1">Edit</span>
                                                                </button>
                                                            </Link>
                                                        )}
                                                    </div>
                                                </div>
                                            </Col>
                                        </li>
                                    ))}
                            </ul>
                        </div>
                    </div>
                </div>
            </Col>
        </>
    );
}
