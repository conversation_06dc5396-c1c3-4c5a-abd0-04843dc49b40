"use client";
import React, { useState, useEffect, useRef } from "react";
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import {
  SolidInfoIcon,
  LightEyeIcon,
  LicenseIcon,
  DigitaLAssetIcon,
} from "@/assets/svgIcons/SvgIcon";
import Select from "react-select";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import MetaHead from "@/Seo/Meta/MetaHead";
import "@/css/account/MarketPlace.scss";
import Form from "react-bootstrap/Form";
import CommonButton from "@/Components/UI/CommonButton";
import ImageUploader from "@/Components/UI/ImageUploader";

export default function CreateListing() {
  const [fileName, setFileName] = useState("File Upload");
  const [stockLimited, setStockLimited] = useState(false);
  const [digitalFile, setDigitalFile] = useState(true);
  const [downloadLink, setDownloadLink] = useState(false);
  const [tags, setTags] = useState([]);
  const [tagInput, setTagInput] = useState("");
  const [descData, setDescData] = useState("");
  const [licenseData, setLicenseData] = useState("");

  const assetTypeOptions = [
    { value: "stocks", label: "Stocks" },
    { value: "forex", label: "Forex (Foreign Exchange)" },
    { value: "crypto", label: "Cryptocurrency" },
    { value: "options", label: "Options" },
    { value: "futures", label: "Futures" },
    { value: "indices", label: "Indices" },
    { value: "commodities", label: "Commodities" },
    { value: "bonds", label: "Bonds & Fixed Income" },
    {
      value: "general_trading",
      label: "General Trading (Applies to Multiple Markets)",
    },
  ];
  const formatOptions = [
    { value: "stocks", label: "Video Course" },
    { value: "forex", label: "Ebook / PDF Guide" },
    { value: "crypto", label: "Live Webinar" },
    { value: "options", label: "Recorded Webinar" },
    {
      value: "futures",
      label: "Trading Indicator (TradingView, MT4, MT5, etc.)",
    },
    { value: "indices", label: "Trading Bot / Automation Script" },
    { value: "commodities", label: "Spreadsheet / Calculator" },
    { value: "bondsr", label: "Market Report / Research Document" },
    { value: "bondsd", label: "Private Mentorship / Coaching Session" },
    { value: "bondss", label: "Community / Membership Access" },
  ];

  const skillLevelOptions = [
    { value: "stocks", label: "Beginner-Friendly" },
    { value: "forex", label: "Intermediate" },
    { value: "crypto", label: "Advanced" },
    { value: "options", label: "All Levels" },
  ];
  const platformOptions = [
    { value: "stocks", label: "TradingView" },
    { value: "forex", label: "MetaTrader 4 (MT4)" },
    { value: "crypto", label: "MetaTrader 5 (MT5)" },
    { value: "options", label: "cTrader" },
    { value: "futures", label: "ThinkorSwim" },
    { value: "indices", label: "NinjaTrader" },
    { value: "commodities", label: "NinjaTrader" },
    { value: "bondsr", label: "Python / Algorithmic Trading" },
    { value: "bondsd", label: "Excel / Google Sheets" },
    { value: "bondss", label: "General (Not Platform-Specific)" },
  ];
  const styleptions = [
    { value: "day_trading", label: "Day Trading" },
    { value: "swing_trading", label: "Swing Trading" },
    { value: "scalping", label: "Scalping" },
    { value: "trend_trading", label: "Trend Trading" },
    { value: "algo_bot_trading", label: "Algorithmic & Bot Trading" },
    { value: "options_selling", label: "Options Selling / Income Strategies" },
    { value: "crypto_arbitrage", label: "Crypto Arbitrage & DeFi Trading" },
    { value: "long_term_investing", label: "Long-Term Investing" },
    {
      value: "market_psychology",
      label: "Market Psychology & Risk Management",
    },
  ];

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setFileName(file.name);
    }
  };

  const getTruncatedFileName = (name, maxLength = 25) => {
    return name.length > maxLength
      ? name.slice(0, maxLength - 3) + "..."
      : name;
  };

  const getFile = (file) => {
    console.log(file);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
  };
  const indicatorSeparatorStyle = {
    alignSelf: "stretch",
    backgroundColor: "gray",
    marginBottom: 8,
    marginTop: 8,
    width: 1,
  };
  const IndicatorSeparator = ({ innerProps }) => {
    return <span style={indicatorSeparatorStyle} {...innerProps} />;
  };

  const handleTagKeyDown = (e) => {
    if ((e.key === "Enter" || e.key === ",") && tagInput.trim()) {
      e.preventDefault();
      if (!tags.includes(tagInput.trim())) {
        setTags([...tags, tagInput.trim()]);
      }
      setTagInput("");
    } else if (e.key === "Backspace" && tagInput === "") {
      setTags(tags.slice(0, -1));
    }
  };

  const removeTag = (index) => {
    setTags(tags.filter((_, i) => i !== index));
  };

  const MAX_Descrip_LENGTH = 2000;
  const MAX_License_LENGTH = 500;

  const descRef = useRef(null);
  const licenseRef = useRef(null);

  const autoResize = (ref) => {
    if (ref.current) {
      ref.current.style.height = "auto";
      ref.current.style.height = `${ref.current.scrollHeight}px`;
    }
  };
  useEffect(() => {
    autoResize(descRef);
  }, [descData]);
  useEffect(() => {
    autoResize(licenseRef);
  }, [licenseData]);
  useEffect(() => {
    const handleResize = () => {
      autoResize(descRef);
      autoResize(licenseRef);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const metaArray = {
    noindex: true,
    title: "Create Listing | TradeReply",
    description:
      "View your account overview on TradeReply.com. Access a summary of your account details, recent activity, and important notifications.",
    canonical_link: "https://www.tradereply.com/account/overview",
    og_site_name: "TradeReply",
    og_title: "Account Overview | TradeReply",
    og_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
    twitter_title: "Account Overview | TradeReply",
    twitter_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
  };

  return (
    <>
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="account_overview">
          <SidebarHeading title="Create New Listing" />
          <div className="account_card_create_listing">
            <div className="form-wrapper">
              <form
                onSubmit={(e) => {
                  handleSubmit(e);
                }}
              >
                <ImageUploader getFile={getFile} />
                <input
                  type="text"
                  required
                  placeholder="Title"
                  className="form-input"
                />
                <input
                  required
                  type="number"
                  placeholder="Price"
                  className="form-input"
                />
                <div className="checkbox-wrapper">
                  <input
                    className="custom_checkbox_input form-check-input"
                    type="checkbox"
                    id="selectCheck"
                    style={{ pointerEvents: "auto" }}
                    checked={stockLimited}
                    onChange={(e) => setStockLimited(e.target.checked)}
                  />
                  <label
                    className="name custom_checkbox_label ps-1"
                    htmlFor="selectCheck"
                  >
                    Stock Level Applies
                  </label>
                  <CommonTooltip
                    className="d-flex align-items-center"
                    content="Enable this if your product has a limited quantity available for sale. Leave it off for unlimited digital assets.  "
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
                {stockLimited && (
                  <input
                    type="number"
                    placeholder="Enter available stock (total items on hand)"
                    className="form-input"
                  />
                )}
                <Form.Select className="form-select">
                  <option selected>Category</option>
                  <option>Full refund</option>
                  <option>Partial refund</option>
                  <option>Replacement file or fixed version</option>
                </Form.Select>
                <div className="relative">
                  <textarea
                    className="form-textarea  w-full resize-none overflow-hidden"
                    rows="4"
                    placeholder="Description"
                    ref={descRef}
                    maxLength={MAX_Descrip_LENGTH}
                    value={descData}
                    onChange={(e) => setDescData(e.target.value)}
                  ></textarea>
                  <div className="character-count">
                    Characters left: {descData.length}/{MAX_Descrip_LENGTH}
                  </div>
                </div>
                <div className="checkbox-wrapper">
                  <DigitaLAssetIcon />
                  <label>Digital Asset (Upload or Link)</label>
                  <CommonTooltip
                    className="d-flex align-items-center"
                    content="Choose how your digital product will be delivered to buyers — either by uploading a file or providing a secure external download link."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
                <div className="checkbox-wrapper">
                  <input
                    className="custom_checkbox_input form-check-input"
                    type="checkbox"
                    id="selectCheck"
                    style={{ pointerEvents: "auto" }}
                    checked={digitalFile}
                    onChange={(e) => setDigitalFile(e.target.checked)}
                  />
                  <label
                    className="name custom_checkbox_label ps-1"
                    htmlFor="selectCheck"
                  >
                    Upload Digital File
                  </label>
                  <CommonTooltip
                    className="d-flex align-items-center"
                    content="Upload the actual file you want to deliver to buyers after purchase. Accepted formats include ZIP, PDF, XLSX, DOCX, and more."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
                {digitalFile && (
                  <label className="file-upload-wrapper">
                    {getTruncatedFileName(fileName)}
                    <input
                      type="file"
                      name="file"
                      onChange={handleFileChange}
                    />
                  </label>
                )}
                <div className="checkbox-wrapper">
                  <input
                    className="custom_checkbox_input form-check-input"
                    type="checkbox"
                    id="selectCheck"
                    style={{ pointerEvents: "auto" }}
                    checked={downloadLink}
                    onChange={(e) => setDownloadLink(e.target.checked)}
                  />
                  <label
                    className="name custom_checkbox_label ps-1"
                    htmlFor="selectCheck"
                  >
                    External Download Link
                  </label>
                  <CommonTooltip
                    className="d-flex align-items-center"
                    content="Paste a direct link to your hosted file (e.g., Google Drive, Dropbox). Make sure the link is accessible and does not require login."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
                {downloadLink && (
                  <input
                    type="text"
                    placeholder="Enter URL to digital content (e.g. private video or file link)"
                    className="form-input"
                  />
                )}
                <Form.Group controlId="tradingStyles">
                  <Form.Label>Asset Type</Form.Label>
                  <Select
                    isMulti
                    name="colors"
                    options={assetTypeOptions}
                    className="basic-multi-select form-multi-select mb-3"
                    placeholder="Asset Type"
                    classNamePrefix="select"
                    isSearchable={false}
                    components={{ IndicatorSeparator }}
                  />
                </Form.Group>
                <Form.Group controlId="tradingStyles">
                  <Form.Label>Format</Form.Label>
                  <Select
                    isMulti
                    name="colors"
                    options={formatOptions}
                    className="basic-multi-select form-multi-select mb-3"
                    placeholder="Format"
                    classNamePrefix="select"
                    isSearchable={false}
                    components={{ IndicatorSeparator }}
                  />
                </Form.Group>
                <Form.Group controlId="tradingStyles">
                  <Form.Label>Skill Level</Form.Label>
                  <Select
                    isMulti
                    name="colors"
                    options={skillLevelOptions}
                    className="basic-multi-select form-multi-select mb-3"
                    placeholder="Skill Level"
                    classNamePrefix="select"
                    isSearchable={false}
                    components={{ IndicatorSeparator }}
                  />
                </Form.Group>

                <Form.Group controlId="tradingStyles">
                  <Form.Label>Trading Platform</Form.Label>
                  <Select
                    isMulti
                    name="colors"
                    options={platformOptions}
                    className="basic-multi-select form-multi-select mb-3"
                    placeholder="Trading Platform"
                    classNamePrefix="select"
                    isSearchable={false}
                    components={{ IndicatorSeparator }}
                  />
                </Form.Group>
                <Form.Group controlId="tradingStyles">
                  <Form.Label>Trading Style</Form.Label>
                  <Select
                    isMulti
                    name="colors"
                    options={styleptions}
                    className="basic-multi-select form-multi-select mb-3"
                    placeholder="Trading Style"
                    classNamePrefix="select"
                    isSearchable={false}
                    components={{ IndicatorSeparator }}
                  />
                </Form.Group>
                <Form.Group controlId="tradingStyles">
                  <Form.Label>Time Commitment</Form.Label>
                  <Form.Select className="form-select">
                    <option disabled selected>
                      Time Commitment
                    </option>
                    <option>Part-Time</option>
                    <option>Full-Time</option>
                  </Form.Select>
                </Form.Group>

                <div className="checkbox-wrapper">
                  <LightEyeIcon />
                  <label>Tags</label>
                  <CommonTooltip
                    className="d-flex align-items-center"
                    content="Add descriptive keywords related to your product (e.g., “scalping,” “crypto,” “MACD”). Tags help buyers discover your listing through search and filtering."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
                <div className="tags-input">
                  {tags.map((tag, index) => (
                    <span
                      key={index}
                      className="tag flex items-center gap-1 bg-gray-200 px-2 py-1 rounded"
                    >
                      {tag}
                      <svg
                        onClick={() => removeTag(index)}
                        xmlns="http://www.w3.org/2000/svg"
                        width="15"
                        height="15"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="cursor-pointer"
                      >
                        <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                        <path d="M18 6l-12 12" />
                        <path d="M6 6l12 12" />
                      </svg>
                    </span>
                  ))}
                  <input
                    type="text"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyDown={handleTagKeyDown}
                    placeholder="Add tags to help users find your product"
                    className="outline-none flex-1"
                  />
                </div>
                <div className="checkbox-wrapper">
                  <LicenseIcon />
                  <label>License/Usage Terms</label>
                  <CommonTooltip
                    className="d-flex align-items-center"
                    content="Explain how buyers are allowed to use your product. For example: personal use only, no redistribution, or includes lifetime access."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
                <div className="relative">
                  <textarea
                    className="form-textarea w-full resize-none overflow-hidden"
                    rows="4"
                    placeholder="Describe any usage or redistribution restrictions"
                    ref={licenseRef}
                    maxLength={MAX_License_LENGTH}
                    value={licenseData}
                    onChange={(e) => setLicenseData(e.target.value)}
                  ></textarea>
                  <div className="character-count">
                    Characters left: {licenseData.length}/{MAX_License_LENGTH}
                  </div>
                </div>
                <CommonButton
                  title="Publish"
                  type="submit"
                  className="view_res_btn w-100"
                />
              </form>
            </div>
          </div>
        </div>
      </AccountLayout>
    </>
  );
}
