"use client";
import React from 'react'
import HomeLayout from "@/Layouts/HomeLayout";
import MetaHead from '@/Seo/Meta/MetaHead';
import { Container } from "react-bootstrap";
import { Row, Col } from "react-bootstrap";
import "../../../css/Home/Cart.scss";
import { DeviceMobileSpeaker, BlackErrorCircle, ViewCartBaseBlue, CheckoutCardGray, AccessGray } from "@/assets/svgIcons/SvgIcon";
import CommonButton from '@/Components/UI/CommonButton';
import NavLink from '@/Components/UI/NavLink';
import { useEffect, useState } from "react";
import Cookies from "js-cookie";

export default function page() {
    const [loginToken, setLoginToken] = useState(null);
    useEffect(() => {
        const tokens = Cookies.get("authToken");
        setLoginToken(tokens || null);
    }, []);
    const cartItems = [
        {
            id: 1,
            image: "https://cdn.tradereply.com/dev/user-uploads/marketplace/listing-12345.png",
            name: "Carpenter’s Exotic Animal Formulary",
            format: "Video",
            duration: "Lifetime",
            price: "$99.99",
        },
        {
            id: 2,
            image: "https://cdn.tradereply.com/dev/user-uploads/marketplace/listing-12345.png",
            name: "Advanced Wildlife Medicine",
            format: "E-Book",
            duration: "1 Year",
            price: "$49.99",
        },
    ];

    const metaArray = {
        noindex: true,
        title: "Shopping Cart | Review Your Items | TradeReply",
        description: "Review the items in your TradeReply Marketplace cart before proceeding to checkout. Make sure you have everything you need for successful trading.",
        canonical_link: "https://www.tradereply.com/cart",
        og_site_name: "TradeReply",
        og_title: "Shopping Cart | Review Your Items | TradeReply Marketplace",
        og_description: "Review the items in your TradeReply Marketplace cart before proceeding to checkout. Make sure you have everything you need for successful trading.",
        twitter_title: "Shopping Cart | Review Your Items | TradeReply Marketplace",
        twitter_description: "Review the items in your TradeReply Marketplace cart before proceeding to checkout. Make sure you have everything you need for successful trading."
    };

    return (
        <>
            <HomeLayout>
                <MetaHead props={metaArray} />
                <div className="cart">
                    <Container>
                        <div className='cartContainer'>
                            <div className={`${!loginToken ? "d-none" : ""}`}>
                                <Row>
                                    <Col sm={12} lg={8} className='order-lg-1 order-2'>
                                        <p className='cartContainer_title mt-3 mt-lg-0'>Shopping Cart</p>
                                        <div className='cartContainer_itemsbox'>
                                            <div className='cartContainer_itemsbox_title'>
                                                <p>Your items (2)</p>
                                                <p>USD</p>
                                            </div>
                                            <div className='cartContainer_itemsbox_inner'>
                                                {cartItems.map((item) => (
                                                    <div key={item.id} className="cartContainer_itemsbox_inner_box">
                                                        <div className="d-flex gap-3">
                                                            <img className='itemImg' src={item.image} alt="Cart Image" />
                                                            <div>
                                                                <p className="itemName">{item.name}</p>
                                                                <p className="itemFormat">
                                                                    Format: <span>{item.format}</span>
                                                                </p>
                                                                <p className="itemDuration">
                                                                    Access Duration: <span>{item.duration}</span>
                                                                </p>
                                                                <div className="itemLicense">
                                                                    <DeviceMobileSpeaker />
                                                                    <p>This is a lifetime access license</p>
                                                                    <BlackErrorCircle />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="cartContainer_itemsbox_inner_box_right">
                                                            <p className="mb-0 order-md-1 order-2">{item.price}</p>
                                                            <button className="mb-0 order-md-2 order-1">Remove</button>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    </Col>
                                    <Col sm={12} lg={4} className='order-lg-2 order-1'>
                                        <div className='cartContainer_itemsbox_right'>
                                            <div className='cartContainer_itemsbox_right_top'>
                                                <div className='d-flex flex-column align-items-center'>
                                                    <div className='cart_steps baseblue_border'>
                                                        <ViewCartBaseBlue />
                                                    </div>
                                                    <p className='blue_text'>View Cart</p>
                                                </div>
                                                <div className='border-gray col'></div>
                                                <div className='d-flex flex-column align-items-center'>
                                                    <div className='cart_steps darkgray_border'>
                                                        <CheckoutCardGray />
                                                    </div>
                                                    <p className='darkgrey_text'>Checkout </p>
                                                </div>
                                                <div className='border-gray col'></div>
                                                <div className='d-flex flex-column align-items-center'>
                                                    <div className='cart_steps darkgray_border'>
                                                        <AccessGray />
                                                    </div>
                                                    <p className='darkgrey_text'>Access</p>
                                                </div>
                                            </div>
                                            <div className='cartContainer_itemsbox_right_inner'>
                                                <div className='orderSummary'>
                                                    <p>Order Summary</p>
                                                    <p>USD</p>
                                                </div>
                                                <div className='subtotal'>
                                                    <p>Subtotal:</p>
                                                    <span>$140.99</span>
                                                </div>
                                                <div className='tax'>
                                                    <p>Tax:</p>
                                                    <span>calculated in checkout</span>
                                                </div>
                                                <div className='orderTotal'>
                                                    <p>Order Total:</p>
                                                    <p>$140.99</p>
                                                </div>
                                            </div>
                                            <div className='d-none d-lg-block'>
                                                <div className='my-4'>
                                                    <CommonButton
                                                        title="Sign In and Checkout"
                                                        fluid
                                                    />
                                                </div>
                                                <div className='text-center'>
                                                    <NavLink href="#">
                                                        Continue Shopping
                                                    </NavLink>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                        </div>
                                    </Col>
                                </Row>
                                <div className='d-lg-none d-block'>
                                    <div className='my-4'>
                                        <CommonButton
                                            title="Sign In and Checkout"
                                            fluid
                                        />
                                    </div>
                                    <div className='text-center'>
                                        <NavLink href="#">
                                            Continue Shopping
                                        </NavLink>
                                    </div>
                                </div>
                            </div>
                            <div className={`cartLogOut ${loginToken ? "d-none" : ""}`}>
                                <img className='cartLogOut_image' src="https://cdn.tradereply.com/dev/site-assets/tradereply-learn-trading-strategies.png" alt="Tradereply Cart Image" />
                                <div className='cartLogOut_text'>
                                    <p className='heading'>Your TradeReply Cart is empty</p>
                                    <NavLink href="/marketplace">
                                        <p className='subHeading mt-2'>Discover trading courses, eBooks and more from fellow traders in our marketplace.</p>
                                    </NavLink>
                                    <div className='mt-3 d-flex gap-3'>
                                        <NavLink href="/login">
                                            <CommonButton
                                                title="Sign In to your account"
                                                fluid
                                            />
                                        </NavLink>
                                        <NavLink href="/signup">
                                            <CommonButton
                                                title="Sign up now"
                                                whiteBtn
                                            />
                                        </NavLink>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Container>
                </div>
            </HomeLayout>
        </>
    )
}
