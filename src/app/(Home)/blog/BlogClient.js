"use client";

import { Col, Container, Row } from "react-bootstrap";
import PostCard from "@/Components/common/Home/PostCard";
import RecentPost from "@/Components/common/Home/RecentPost";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import CustomPagination from "@/Components/UI/CustomPagination";
import HomeLayout from "@/Layouts/HomeLayout";
import "@/css/Home/Blog.scss";
import { get, isEmpty, map } from "lodash";
import NoRecord from "@/Components/UI/NoRecord";
import { useState, useEffect, useCallback } from "react";
import { get as GET } from "@/utils/apiUtils";
import Loader from "@/Components/common/Loader";
import debounce from "lodash.debounce";
import { useRouter } from "next/navigation";
import MetaHead from "@/Seo/Meta/MetaHead";

const BlogClient = ({ initialData, currentPage }) => {
  const router = useRouter();

  const [loading, setLoading] = useState(false);
  const [BlogMeta, setBlogMeta] = useState(initialData.meta);
  const [BlogArticles, setBlogArticles] = useState(initialData.latest_blogs);
  const [recentBlogArticles, setRecentBlogArticles] = useState(initialData.top_blogs);
  const [newPage, setNewPage] = useState(currentPage);

  const fetchBlog = async (page) => {
    setLoading(true);
    try {
      const response = await GET(`/article?page=${page}&type=blog`);
      setBlogArticles(response?.data?.latest_blogs || []);
      setRecentBlogArticles(response?.data?.top_blogs || []);
      setBlogMeta(response?.data?.meta || {});
    } catch (error) {
      console.error("Failed to fetch blogs:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBlog(newPage);
  }, [newPage]);

  const handleDataFromChild = (childPage) => {
    const newPage = parseInt(childPage);
    window.scrollTo(0, 0);
    const path = newPage === 1 ? `/blog` : `/blog/page/${newPage}`;
    router.push(path, undefined, { shallow: true });
    setNewPage(newPage);
  };

  return (
    <div className="blog py-100">
      <Container>
        <div className="blog_cards">
          <Row>
            {recentBlogArticles?.map((item, index) => (
              <Col xs={12} md={6} lg={4} className="mb-md-4 mb-1" key={index}>
                <PostCard
                  img={item.feature_image_url}
                  title={item?.primary_category?.title ?? 'N/A'}
                  text={item?.title}
                  className="w-100"
                  redirectHref={`/blog/${item.slug}`}
                />
              </Col>
            ))}
          </Row>
        </div>

        <div className="blog_recent_post">
          <div className="w-100 d-flex justify-content-between align-items-center">
            <h1 className="recontPostTitle">Recent Publications</h1>
            <div className="blog_pagination justify-content-center justify-content-md-end d-flex mt-0 mb-4">
              <CustomPagination links={BlogMeta} onDataSend={handleDataFromChild} pageUrl="blog/page/" />
            </div>
          </div>

          <Row>
            {loading ? (
              <div className="w-100 d-flex justify-content-center">
                <Loader />
              </div>
            ) : BlogArticles.length > 0 ? (
              BlogArticles.map((item) => (
                <Col xs={12} key={item.id}>
                  <RecentPost
                    img={item?.feature_image_url}
                    title={item?.title}
                    category={item?.primary_category?.title}
                    text={item?.summary}
                    coinname={item?.coinname}
                    href={`/blog/${item.slug}`}
                  />
                </Col>
              ))
            ) : (
              <NoRecord />
            )}
          </Row>

          <div className="blog_pagination justify-content-center justify-content-md-end d-flex mt-0">
            <CustomPagination
              useLinks={false}
              links={BlogMeta}
              onDataSend={handleDataFromChild}
              pageUrl="blog/page/"
              noMeta={true}
            />
          </div>
        </div>
      </Container>
    </div>
  );
};

export default BlogClient;
