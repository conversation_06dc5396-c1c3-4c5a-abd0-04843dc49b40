"use client";

import { Col, Container, Row } from "react-bootstrap";
import CommonSearch from "@/Components/UI/CommonSearch";
import CustomPagination from "@/Components/UI/CustomPagination";
import RecentPost from "@/Components/common/Home/RecentPost";
import { useCallback, useEffect, useRef, useState } from "react";
import { RightArrowIcon, CrossIcon } from "@/assets/svgIcons/SvgIcon";
import HomeLayout from "@/Layouts/HomeLayout";
import { get } from "@/utils/apiUtils";
import "../../../css/Home/Category.scss";
import { useRouter } from "next/navigation";
import Loader from "@/Components/common/Loader";
import Link from "next/link";
import { debounce } from "lodash";
import MetaHead from "@/Seo/Meta/MetaHead";
import { getCookie, setCookie, deleteCookie } from "cookies-next";

const CategoryClient = ({ initialData, slug, keyWord, currentPage, metaArray: initialMetaArray, }) => {
  const router = useRouter();
  const sliderRef = useRef(null);
  const [disableLeft, setDisableLeft] = useState(true);
  const [disableRight, setDisableRight] = useState(false);
  const [listingAllCategories, setListingAllCategories] = useState(initialData.allcategories);
  const [allCategoryArticles, setAllCategoryArticles] = useState(initialData.articles);
  const [categoryMeta, setCategoryMeta] = useState(initialData.meta);
  const [selectedCategory, setSelectedCategory] = useState(initialData.selected_category);
  const [selectedFilter, setSelectedFilter] = useState(initialData.selected_category?.title || "All");
  const [searchInputValue, setSearchInputValue] = useState(keyWord);
  const [searchKeyword, setSearchKeyword] = useState(keyWord);
  const [newPage, setNewPage] = useState(currentPage);
  const [isLoading, setIsLoading] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [metaArray, setMetaArray] = useState(initialMetaArray || {});

  const hasSearch = searchKeyword.trim() !== "";
  console.log("initial data??", initialData)
  console.log("initial categoryMeta??", categoryMeta)

  // useEffect(() => {
  //   const storedKeyword = getCookie("categorySearchKey");
  //   if (storedKeyword) setSearchKeyword(storedKeyword);
  // }, []);
  useEffect(() => {
    const searchKey = getCookie("categorySearchKey");
    const categoryId = getCookie("category-id");

    const effectiveSearchKey = searchKey || categoryId || "";

    if (effectiveSearchKey) {
        setSearchKeyword(effectiveSearchKey);
        setSearchInputValue(effectiveSearchKey);
        setCookie("categorySearchKey", effectiveSearchKey, { path: "/", sameSite: "lax" }); // Always sync category-id
        deleteCookie("category-id", { path: "/" }); // Clear temp cookie after use
    }
}, []);


  useEffect(() => {
    if (
      newPage === currentPage &&
      searchKeyword === keyWord &&
      allCategoryArticles.length > 0
    ) {
      return;
    }

    const fetchCategories = async () => {
      setIsLoading(true);
      try {
        const response = await get("/category", {
          slug,
          key: searchKeyword,
          page: newPage,
        });
        setListingAllCategories(response?.data?.allcategories);
        setAllCategoryArticles(response?.data?.articles);
        setCategoryMeta(response?.data?.meta);

        setSelectedCategory(response?.data?.selected_category);
        setSelectedFilter(response?.data?.selected_category?.title || "All");

        // Update meta dynamically
        const isSearch = searchKeyword.trim() !== "";
        const isOnBaseCategory = !slug; // more accurate

        //  for searching link

        setMetaArray({
          title: "TradeReply Categories | Explore Trading Content",
          description: "Explore curated content on TradeReply.com. Browse blog articles and educational resources grouped by category to deepen your trading knowledge.",
          og_title: "TradeReply Categories | Explore Trading Content",
          og_description: "Explore curated content on TradeReply.com. Browse blog articles and educational resources grouped by category to deepen your trading knowledge.",
          og_site_name: "TradeReply",
          twitter_title: "TradeReply Categories | Explore Trading Content",
          twitter_description: "Explore curated content on TradeReply.com. Browse blog articles and educational resources grouped by category to deepen your trading knowledge.",
          noindex: isSearch || !isOnBaseCategory,
          canonical_link: isSearch || !isOnBaseCategory
            ? "" // 🔥 Clear canonical link when search is active
            : `https://dev.tradereply.com/category/page/${newPage}`,

        });
      } catch (error) {
        console.error("Category fetch error:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, [searchKeyword, slug]);

  useEffect(() => {
    const checkScreenSize = () => setIsMobile(window.innerWidth <= 768);
    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  const smoothScroll = (amount) => {
    const el = sliderRef.current;
    if (el) el.scrollTo({ left: el.scrollLeft + amount, behavior: "smooth" });
  };

  const scrollLeft = () => smoothScroll(isMobile ? -100 : -200);
  const scrollRight = () => smoothScroll(isMobile ? 100 : 200);

  useEffect(() => {
    const el = sliderRef.current;
    if (!el) return;
    const handleScroll = () => {
      const { scrollLeft, scrollWidth, clientWidth } = el;
      setDisableLeft(scrollLeft === 0);
      setDisableRight(scrollLeft + clientWidth >= scrollWidth);
    };
    el.addEventListener("scroll", handleScroll);
    handleScroll();
    return () => el.removeEventListener("scroll", handleScroll);
  }, []);

  const handleHomeSearch = (event) => {
    const value = event.target.value;
    setSearchInputValue(value);
    debouncedSearch(value);
  };


  const debouncedSearch = useCallback(
    debounce((searchTerm) => {
      setSearchKeyword(searchTerm);
      setNewPage(1);
      setCookie("categorySearchKey", searchTerm, { path: "/", sameSite: "lax" });

      // Navigate to base category or slug page, no search query in URL
      // const basePath = slug ? `/category?slug=${slug}` : "/category";
      // router.push(basePath);
    }, 400),
    [slug]
  );

  const handleDataFromChild = (childPage) => {
    // setNewPage(childPage);
    setCookie("categorySearchKey", searchKeyword, { path: "/", sameSite: "lax" });

    let url = slug ? `/category?slug=${slug}` : `/category`;
    if (childPage > 1) {
      url = slug
        ? `/category/page/${childPage}?slug=${slug}`
        : `/category/page/${childPage}`;
    }
    const isSamePage = page === newPage;
    if (!isSamePage) {
      setNewPage(page); // let useEffect trigger
      router.push(url);
    } else {
      // 👇 Force data refresh
      setNewPage(page); // Still set it, in case internal state reset
      fetchEducation(page, searchKeyword); // Force fetch manually
    }

  };

  const fetchCategories = async (page = 1, keyword = "") => {
    setIsLoading(true);
    try {
      const response = await get("/category", {
        slug,
        key: keyword,      // ✅ use the parameter
        page: page,        // ✅ use the parameter
      });
      setListingAllCategories(response?.data?.allcategories);
      setAllCategoryArticles(response?.data?.articles);
      setCategoryMeta(response?.data?.meta);
      setSelectedCategory(response?.data?.selected_category);
      setSelectedFilter(response?.data?.selected_category?.title || "All");
    } catch (error) {
      console.error("Category fetch error:", error);
    } finally {
      setIsLoading(false);
    }
  };


  const handleClearSearch = async () => {
    deleteCookie("categorySearchKey", { path: "/" });
// Also clear category-id only if you're on a slug page
    if (slug) {
      deleteCookie("category-id", { path: "/" });
    }
    const isAlreadyOnCategory = window.location.pathname === "/category";
    if (isAlreadyOnCategory) {
      setSearchInputValue("");
      setSearchKeyword("");
      setNewPage(1);
      fetchCategories(1, ""); // Manually fetch page 1 data when already on /education

    } else {
      setIsLoading(true)
      await router.push("/category");
      // Set newPage here if needed
    }
  };
  return (
    <HomeLayout>
      <MetaHead props={metaArray} />
      <section className="categorySec py-100">
        <Container>
          <div className="categorySec_heading text-center">
            <h1>TradeReply Categories</h1>
            <p>Browse categories to find relevant articles and insights.</p>
            <div className="categorySec_search">
              <CommonSearch
                placeholder="Search for terms"
                icon={true}
                onChange={handleHomeSearch}
                value={searchInputValue}
                name="category"
                onClear={handleClearSearch}

              />
            </div>
          </div>

          <div className="categorySec_fliters">
            <div className="categorySec_fliters_inner">
              <button className={`scroll-btn left ${disableLeft ? "disabled" : ""}`} onClick={scrollLeft}><RightArrowIcon /></button>
              <div className="slider" ref={sliderRef}>
                <Link href="/category" className={`categorySec_fliters_boxbutton ${!selectedCategory ? "active" : ""}`}>All</Link>
                {listingAllCategories?.map((item) => (
                  <div key={item?.id} className={`categorySec_fliters_boxbutton text-nowrap ${item.slug === selectedCategory?.slug ? "active" : ""}`}>
                    {/* <Link href={`/category/${item.slug}`}
                      onClick={() => {
                        setCookie("categorySearchKey", searchKeyword, { path: "/", sameSite: "lax" });
                        setIsLoading(true);
                        router.push(`/category/${item.slug}`);
                      }}   
                         >
                      {item?.title}
                    </Link> */}
                    <span
                      className="cursor-pointer"
                      onClick={() => {
                        if (searchKeyword.trim()) {
                          setCookie("category-id", searchKeyword.trim(), { path: "/", sameSite: "lax" });
                          setIsLoading(true);
                        } else {
                          deleteCookie("category-id", { path: "/" });
                        }
                        router.push(`/category/${item.slug}`);
                      }}
                    >
                      {item?.title}
                    </span>

                  </div>
                ))}
              </div>
              <button className={`scroll-btn right ${disableRight ? "disabled" : ""}`} onClick={scrollRight}><RightArrowIcon /></button>
            </div>

            <div className="flex gap-3">
              {selectedFilter && (
                <div className="categorySec_fliters_boxadd">
                  <h6 className="d-flex align-items-center flex-wrap gap-3">
                    <span className="d-flex align-items-center gap-2">
                      <span>Filter:</span>
                      <span>{selectedFilter}</span>
                      {selectedFilter !== "All" && (
                        <span className="ml-2 pe-auto cursor-pointer" onClick={() => {
                          setNewPage(1);
                          router.push("/category");
                        }}>
                          <CrossIcon />
                        </span>
                      )}
                    </span>
                  </h6>
                </div>
              )}
              {hasSearch && (
                <div className="categorySec_fliters_boxadd">
                  <h6 className="d-flex align-items-center flex-wrap gap-3">
                    <span className="d-flex align-items-center gap-2">
                      <span>Search:</span>
                      <span>{searchInputValue}</span>
                      <span className="ml-2 pe-auto cursor-pointer" onClick={handleClearSearch}>
                        <CrossIcon />
                      </span>
                    </span>
                  </h6>
                </div>
              )}
            </div>
          </div>

          {/* data of API Response */}
          <div className="categorySec_term">
            <div className="categorySec_term_content">
              <h4>{selectedCategory ? `Category – ${selectedCategory?.title}` : "All Categories"}</h4>
              <p className="mb-5 mt-3">{selectedCategory?.content || "No category selected"}</p>
            </div>

            <div className="d-flex justify-content-end w-100 mb-4">
              <CustomPagination
                links={categoryMeta}
                onDataSend={handleDataFromChild}
                pageUrl="category"
                metaArray={metaArray}
                flag={!!searchKeyword}
                useDynamicParams={true}
              />
            </div>

            <div className="blog_recentPost">
              {isLoading ? (
                <div>
                  <Loader />
                </div>
              ) : (
                allCategoryArticles?.map((item, index) => (
                  <div key={index}>
                    <Row>
                      <Col xs={12} className="d-flex">
                        <RecentPost
                          img={item.feature_image_url}
                          title={item?.title}
                          text={item?.summary}
                          href={item.type === "education" ? `/education/${item?.slug}` : `/blog/${item?.slug}`}
                        />
                      </Col>
                    </Row>
                  </div>
                ))
              )}
            </div>


            <div className="d-flex justify-content-end w-100">
              <CustomPagination
                links={categoryMeta}
                onDataSend={handleDataFromChild}
                pageUrl="category"
                metaArray={metaArray}
                flag={!!searchKeyword}
                useDynamicParams={true}

              />
            </div>
          </div>
        </Container>
      </section>
    </HomeLayout>
  );
};

export default CategoryClient;
