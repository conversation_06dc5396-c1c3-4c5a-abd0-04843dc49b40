"use client";
import React from 'react'
import HomeLayout from "@/Layouts/HomeLayout";
import MetaHead from '@/Seo/Meta/MetaHead';
import { Container } from "react-bootstrap";
import { Row, Col } from "react-bootstrap";
import "@/css/Home/Checkout.scss";
import { DeviceMobileSpeaker, BlackErrorCircle, ViewCartDarkBlue, CheckoutCardBaseBlue, AccessGray } from "@/assets/svgIcons/SvgIcon";
import CommonButton from '@/Components/UI/CommonButton';
import NavLink from '@/Components/UI/NavLink';
import { checkoutSchema } from "@/validations/schema";
import { Formik, Field, Form } from "formik";
import InputError from '@/Components/UI/InputError';
import TextInput from '@/Components/UI/TextInput';
import InputLabel from '@/Components/UI/InputLabel';

const initialValues = {
    firstName: "",
    lastName: "",
    country: "",
    address: "",
    cardNumber: "",
    expireDate: "",
    securityCode: ""
};

export default function page() {
    const countries = [
        { code: "select", name: "Select your country" },
        { code: "AF", name: "Afghanistan" },
        { code: "AL", name: "Albania" },
        { code: "DZ", name: "Algeria" },
        { code: "US", name: "United States" },
        { code: "CA", name: "Canada" },
        { code: "GB", name: "United Kingdom" },
        { code: "AU", name: "Australia" },
        { code: "IN", name: "India" },
        { code: "PK", name: "Pakistan" },
        { code: "FR", name: "France" },
        { code: "DE", name: "Germany" },
        { code: "IT", name: "Italy" },
        { code: "JP", name: "Japan" },
        { code: "CN", name: "China" },
        { code: "RU", name: "Russia" },
        { code: "ZA", name: "South Africa" },
        { code: "BR", name: "Brazil" },
        { code: "MX", name: "Mexico" },
        { code: "NG", name: "Nigeria" },
        { code: "KR", name: "South Korea" },
    ];

    const cartItems = [
        {
            id: 1,
            image: "https://cdn.tradereply.com/dev/user-uploads/marketplace/listing-12345.png",
            name: "Carpenter’s Exotic Animal Formulary",
            format: "Video",
            duration: "Lifetime",
            price: "$99.99",
        },
        {
            id: 2,
            image: "https://cdn.tradereply.com/dev/user-uploads/marketplace/listing-12345.png",
            name: "Advanced Wildlife Medicine",
            format: "E-Book",
            duration: "1 Year",
            price: "$49.99",
        },
    ];

    const submit = async (values, { setSubmitting, setErrors }) => {
        const response = await dispatch(loginUser(values));
        setSubmitting(false);
    };

    const metaArray = {
        noindex: true,
        title: "Checkout | Secure Your Purchase | TradeReply",
        description: "Complete your purchase securely at TradeReply Marketplace. Enter your details and confirm your order to access top trading tools and services.",
        canonical_link: "https://www.tradereply.com/checkout",
        og_site_name: "TradeReply",
        og_title: "Checkout | Secure Your Purchase | TradeReply Marketplace",
        og_description: "Complete your purchase securely at TradeReply Marketplace. Enter your details and confirm your order to access top trading tools and services.",
        twitter_title: "Checkout | Secure Your Purchase | TradeReply Marketplace",
        twitter_description: "Complete your purchase securely at TradeReply Marketplace. Enter your details and confirm your order to access top trading tools and services.",
    };

    return (
        <>
            <HomeLayout>
                <MetaHead props={metaArray} />
                <div className="checkout">
                    <Container>
                        <div className='checkoutContainer'>
                            <div >
                                <Row>
                                    <Col sm={12} lg={6}>
                                        <p className='checkoutContainer_title'>Checkout</p>
                                        <p className='checkoutContainer_subtitle'>Once your order is complete, your digital resource will be instantly accessible.</p>
                                    </Col>
                                    <Col sm={12} lg={6}>
                                        <div className='checkoutContainer_top'>
                                            <div className='checkoutContainer_top_right'>
                                                <div className='d-flex flex-column align-items-center'>
                                                    <div className='cart_steps darkblue_border'>
                                                        <ViewCartDarkBlue />
                                                    </div>
                                                    <p className='darkblue_text'>View Cart</p>
                                                </div>
                                                <div className='border-blue col'></div>
                                                <div className='d-flex flex-column align-items-center'>
                                                    <div className='cart_steps baseblue_border'>
                                                        <CheckoutCardBaseBlue />
                                                    </div>
                                                    <p className='blue_text'>Checkout </p>
                                                </div>
                                                <div className='border-gray col'></div>
                                                <div className='d-flex flex-column align-items-center'>
                                                    <div className='cart_steps darkgray_border'>
                                                        <AccessGray />
                                                    </div>
                                                    <p className='darkgrey_text'>Access</p>
                                                </div>
                                            </div>
                                        </div>
                                    </Col>
                                </Row>
                                <Row className='mt-2'>
                                    <Col sm={12} lg={6} className='order-lg-1 order-2 mt-3 mt-lg-0'>
                                        <div className='checkoutContainer_billing'>
                                            <Formik
                                                initialValues={initialValues}
                                                validationSchema={checkoutSchema}
                                                // onSubmit={submit}
                                                onSubmit={(values) => console.log("Submitted:", values)}
                                            >
                                                {({ touched, errors, isSubmitting, values, setFieldValue }) => (
                                                    <Form>
                                                        <p className='checkoutContainer_billing_title'>Billing Address</p>
                                                        <Field name="firstName">
                                                            {({ field }) => (
                                                                <>
                                                                    <InputLabel value="First Name" />
                                                                    <TextInput
                                                                        {...field}
                                                                        placeholder="Enter your first name"
                                                                        type="text"
                                                                        error={touched.firstName && errors.firstName ? <InputError message={errors.firstName} /> : null}
                                                                        isError={touched.firstName && errors.firstName}
                                                                    />
                                                                </>
                                                            )}
                                                        </Field>
                                                        <Field name="lastName">
                                                            {({ field }) => (
                                                                <>
                                                                    <InputLabel value="Last Name" />
                                                                    <TextInput
                                                                        {...field}
                                                                        placeholder="Enter your last name"
                                                                        type="text"
                                                                        error={touched.lastName && errors.lastName ? <InputError message={errors.lastName} /> : null}
                                                                        isError={touched.lastName && errors.lastName}
                                                                    />
                                                                </>
                                                            )}
                                                        </Field>
                                                        <div className="customInput">
                                                            <Field name="country">
                                                                {({ field }) => (
                                                                    <>
                                                                        <InputLabel value="Country Or Region" />
                                                                        <select
                                                                            {...field}
                                                                            className={`customSelect form-select ${errors.country ? "error-field" : ""}`}
                                                                        >
                                                                            {countries.map((country) => (
                                                                                <option key={country.code} value={country.code}>
                                                                                    {country.name}
                                                                                </option>
                                                                            ))}
                                                                        </select>

                                                                        {touched.country && errors.country && <InputError message={errors.country} />}
                                                                    </>
                                                                )}
                                                            </Field>
                                                        </div>
                                                        <Field name="address">
                                                            {({ field }) => (
                                                                <>
                                                                    <InputLabel value="Address" />
                                                                    <TextInput
                                                                        {...field}
                                                                        placeholder="Street, Apartment, City, State, ZIP"
                                                                        type="text"
                                                                        error={touched.address && errors.address ? <InputError message={errors.address} /> : null}
                                                                        isError={touched.address && errors.address}
                                                                    />
                                                                </>
                                                            )}
                                                        </Field>
                                                        <p className='checkoutContainer_billing_title'>Payment Information</p>
                                                        <div className="customInput">
                                                            <Field name="cardNumber">
                                                                {({ field }) => (
                                                                    <>
                                                                        <InputLabel value="Card Number" />
                                                                        <input
                                                                            {...field}
                                                                            className={`form-control ${errors.cardNumber ? "error-field" : ""}`}
                                                                            placeholder="1234 5678 9012 3456"
                                                                            maxLength="19" // 16 digits + 3 spaces
                                                                            inputMode="numeric" // Opens numeric keyboard on mobile
                                                                            value={values.cardNumber} // Ensure controlled input
                                                                            onChange={(e) => {
                                                                                let value = e.target.value.replace(/\D/g, ""); // Remove non-numeric characters
                                                                                value = value.replace(/(.{4})/g, "$1 ").trim(); // Auto-space every 4 digits
                                                                                setFieldValue("cardNumber", value); // Update Formik state
                                                                            }}
                                                                        />
                                                                        {touched.cardNumber && errors.cardNumber ? <InputError message={errors.cardNumber} /> : null}
                                                                    </>
                                                                )}
                                                            </Field>
                                                        </div>
                                                        <Row>
                                                            <Col xs={6} className="customInput">
                                                                <Field name="expireDate">
                                                                    {({ field }) => (
                                                                        <>
                                                                            <InputLabel value="Expiration Date" />
                                                                            <input
                                                                                {...field}
                                                                                className={`form-control ${errors.expireDate ? "error-field" : ""}`}
                                                                                placeholder="MM / YYYY"
                                                                                maxLength="9" // "MM / YYYY" (7 characters + 2 optional spaces)
                                                                                value={values.expireDate}
                                                                                onChange={(e) => {
                                                                                    let value = e.target.value.replace(/\D/g, ""); // Remove non-numeric characters
                                                                                    if (value.length > 2) {
                                                                                        value = value.replace(/(\d{2})/, "$1 / "); // Auto add " / "
                                                                                    }
                                                                                    setFieldValue("expireDate", value); // Update Formik state
                                                                                }}
                                                                            />
                                                                            {touched.expireDate && errors.expireDate ? <InputError message={errors.expireDate} /> : null}
                                                                        </>
                                                                    )}
                                                                </Field>
                                                            </Col>
                                                            <Col xs={6} className="customInput">
                                                                <Field name="securityCode">
                                                                    {({ field }) => (
                                                                        <>
                                                                            <InputLabel value="Security Code" />
                                                                            <input
                                                                                {...field}
                                                                                className={`form-control ${errors.securityCode ? "error-field" : ""}`}
                                                                                placeholder="CVC"
                                                                                maxLength="3"
                                                                                inputMode="numeric"
                                                                                value={values.securityCode}
                                                                                onChange={(e) => {
                                                                                    let value = e.target.value.replace(/\D/g, ""); // Only numbers
                                                                                    setFieldValue("securityCode", value); // Update Formik state
                                                                                }}
                                                                            />
                                                                            {touched.securityCode && errors.securityCode ? <InputError message={errors.securityCode} /> : null}
                                                                        </>
                                                                    )}
                                                                </Field>
                                                            </Col>
                                                        </Row>
                                                        <div className="custom_checkbox">
                                                            <input className="custom_checkbox_input form-check-input" type="checkbox" value="" />
                                                            <label className="custom_checkbox_label mb-0">
                                                                Remember payment information
                                                            </label>
                                                        </div>
                                                        <p className='black_text font-semibold'>By placing this order I understand that I am purchasing a license to access this product under the terms provided and not ownership of the product.
                                                            <NavLink href="#" className='text-underline'>Details</NavLink>
                                                        </p>
                                                        <CommonButton className="mt-3"
                                                            type="submit"
                                                            title={isSubmitting ? 'Loading' : 'Place Order'}
                                                            fluid
                                                            disabled={isSubmitting}
                                                        />
                                                    </Form>
                                                )}
                                            </Formik>
                                        </div>
                                    </Col>
                                    <Col sm={12} lg={6} className='order-lg-2 order-1 mt-3 mt-lg-0'>
                                        <div className='checkoutContainer_orderSection'>
                                            <div className='orderSummary'>
                                                <p>Order Summary</p>
                                                <p>USD</p>
                                            </div>
                                            <div className='checkoutContainer_orderSection_items'>
                                                {cartItems.map((item) => (
                                                    <div key={item.id} className="checkoutContainer_orderSection_items_box">
                                                        <div className="d-flex gap-3">
                                                            <img className='itemImg' src={item.image} alt="Cart Image" />
                                                            <div>
                                                                <p className="itemName">{item.name}</p>
                                                                <p className="itemFormat">
                                                                    Format: <span>{item.format}</span>
                                                                </p>
                                                                <p className="itemDuration">
                                                                    Access Duration: <span>{item.duration}</span>
                                                                </p>
                                                                <div className="itemLicense">
                                                                    <DeviceMobileSpeaker />
                                                                    <p>This is a lifetime access license</p>
                                                                    <BlackErrorCircle />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="checkoutContainer_orderSection_items_box_right">
                                                            <p className="mb-0 order-md-1 order-2">{item.price}</p>
                                                            <button className="mb-0 order-md-2 order-1">Remove</button>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                            <div className='subtotal'>
                                                <p>Subtotal:</p>
                                                <span>$140.99</span>
                                            </div>
                                            <div className='tax'>
                                                <p>Tax:</p>
                                                <span>calculated in checkout</span>
                                            </div>
                                            <div className='orderTotal'>
                                                <p>Order Total:</p>
                                                <p>$140.99</p>
                                            </div>
                                        </div>
                                    </Col>
                                </Row>
                            </div>
                        </div>
                    </Container>
                </div>
            </HomeLayout>
        </>
    )
}
