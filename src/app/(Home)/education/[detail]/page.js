import { unstable_noStore as noStore } from 'next/cache';
import EducationContent from "./components/EducationContent";
import HomeLayout from "@/Layouts/HomeLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import { Container } from "react-bootstrap";
import Cookies from 'js-cookie';

async function fetchEducationDetail(detail) {
   const authToken = Cookies.get("authToken");
  const url = new URL(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/article/education/${detail}`);

  const res = await fetch(url.toString(), {
    cache: "no-store",
  });

  if (!res.ok) throw new Error(`API error: ${res.status}`);
  return res.json();
}

export async function generateMetadata({ params }) {
  noStore();

  const resolvedParams = await params;
  const detailSlug = resolvedParams.detail;
  const data = await fetchEducationDetail(detailSlug);
  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;
  return {
    title: `What is ${data.data.title} | TradeReply Education`,
    description: data.data.summary,
    openGraph: {
      title: `What is ${data.data.title} | TradeReply Education`,
      description: data.data.summary,
      images: [{
                url: data?.data?.feature_image_url || 'https://www.tradereply.com/images/tradereply-trading-analytics-og.jpg', // Replace with actual default image if needed
                width: 1200,
                height: 630,
             }],
    },
     twitter: {
         title: `What is ${data?.data?.title} | TradeReply Education`,
         description: data?.data?.summary,
         site: '@JoinTradeReply',
         images: [data?.data?.feature_image_url || 'https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png'], // Replace with actual default image if needed
       },
       icons: {
                icon: [
                  {
                    url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`,
                    type: "image/x-icon",
                  },
                  {
                    url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`,
                    type: "image/svg+xml",
                  },
                ],
              },
  };
}

export default async function EducationDetail({ params }) {
  noStore();

  const resolvedParams = await params;
  const detailSlug = resolvedParams.detail;

  const data = await fetchEducationDetail(detailSlug);

  return (
    <HomeLayout>
      <Container>
        <EducationContent
          detailSlug={detailSlug}
          articleData={data.data}
          nextArticle={data.next_article}
          avgProgress={data.avgProgress}
        />
      </Container>
    </HomeLayout>
  );
}