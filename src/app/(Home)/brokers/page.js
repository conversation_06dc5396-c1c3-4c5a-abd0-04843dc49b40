
'use client';

import { Container } from "react-bootstrap";
import Link from "next/link";
import {
  SolidInfoIcon,
  RightSolidArrowIcon,
} from "@/assets/svgIcons/SvgIcon";
import CommonSearch from "@/Components/UI/CommonSearch";
import FaqCard from "@/Components/common/Home/FaqCard";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import BrokersTable from "@/Components/UI/BrokersTable";
import "../../../css/Home/Brokers.scss";
import HomeLayout from "@/Layouts/HomeLayout";
import { useEffect, useState } from "react";
import { get } from "@/utils/apiUtils";
import toast from "react-hot-toast";
import BrokersToggle from "@/Components/common/Home/BrokersToggle";
import { filter } from "lodash";
import MetaHead from "@/Seo/Meta/MetaHead";

const Brokers = () => {
  const [Providers, setProviders] = useState([]);
  const [filterProviders, setFilterProviders] = useState([]);
  const [brokerSearch, setBrokerSearch] = useState([]);

  const handleBrokerSearch = (event) => {
    setBrokerSearch(event.target.value);
  };

  useEffect(() => {
    const fetchBrokers = async () => {
      try {
        const response = await get('/thirdpartyprovider');
        setProviders(response.data.providers);
        setFilterProviders(response.data.providers);
      } catch (error) {
        console.error('Failed to fetch brokers:', error);
        setProviders([]);
      }
    };
    fetchBrokers();
  }, []);

  useEffect(() => {
    if (!brokerSearch) {
      setFilterProviders(Providers);
      return;
    }

    const results = GetSearchedBrokers(brokerSearch);
    setFilterProviders(results);
  }, [brokerSearch]);


  const GetSearchedBrokers = (brokerSearch) => {
    return Providers.filter((provider) =>
      provider.name.toLowerCase().includes(brokerSearch.toLowerCase())
    );
  };

  const metaArray = {
    title: "TradeReply Broker Integrations | Connect & Trade",
    description: "See which brokers integrate seamlessly with TradeReply.com. Connect your accounts to unlock advanced trading tools, real-time analytics, and strategy optimization.",
    canonical_link: "https://www.tradereply.com/brokers",
    og_site_name: "TradeReply",
    og_title: "TradeReply Broker Integrations | Connect & Trade",
    og_description: "See the brokers supported by TradeReply and seamlessly integrate your trading accounts for better analytics and strategy management.",
    twitter_title: "TradeReply Broker Integrations | Connect & Trade",
    twitter_description: "See the brokers supported by TradeReply and seamlessly integrate your trading accounts for better analytics and strategy management.",
  };

  return (
    <HomeLayout>
      <MetaHead props={metaArray} />
      <div className="brokers py-100">
        <Container>
          <section className="brokers_inner">
            <div className="brokers_content">
              <h1>TradeReply Supported Brokers & Integrations</h1>
              <p>
                Connect your TradeReply.com account to your brokers and trading
                platforms to automatically sync your trades, ensuring all your
                trading data is seamlessly integrated for comprehensive analysis
                and performance tracking. To connect an account,{" "}
                <Link href="/login">Login</Link> or{" "}
                <Link href="/signup"> Create an Account</Link>.
              </p>
              <div className="brokers_content_search">
                <CommonSearch
                  placeholder="Search for Broker or Integration"
                  icon={true}
                  onChange={handleBrokerSearch}
                  name={"brokers"}
                />
              </div>
              <h6>Missing an integration?
                <Link
                  href="#"
                  className="d-inline-flex align-items-center submit_request px-2"
                >
                  Submit a Request{" "}
                  <span className="ms-1">
                    <RightSolidArrowIcon />
                  </span>
                </Link>
              </h6>

            </div>

            <div className="brokers_tablesec">
              <div className="brokers_tablesec_inner">
                <div className="d-flex align-items-center justify-content-end mb-3 ">
                  <CommonTooltip
                    className="d-flex align-items-center"
                    content="Manual entry lets you input trading data using a form builder or upload via Template or Broker CSV files. AutoSync connects directly to your broker via API, automatically updating your data in real-time with limited manual effort."
                    position="top-left"
                  >
                    <SolidInfoIcon />
                    <h6 className="mb-0 ms-3">
                      Sync Options: Manual vs. AutoSync
                    </h6>
                  </CommonTooltip>
                </div>
              </div>
              <div className="d-md-block d-none">
                <BrokersTable providers={filterProviders} />
              </div>
              <div className="d-md-none d-block">
                <BrokersToggle providers={Providers} />
              </div>
            </div>

            <div className="brokers_faqs">
              <FaqCard isPricing={false} />
            </div>
          </section>
        </Container>
      </div>
    </HomeLayout>
  );
};

export default Brokers;
