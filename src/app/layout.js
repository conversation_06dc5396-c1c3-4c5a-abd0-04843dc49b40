import { Suspense } from "react";
import InactivityHandler from "../utils/inactivityHandler";
import Script from "next/script";
import "bootstrap/dist/css/bootstrap.min.css";
import "./globals.scss";
import "../../src/css/app.scss";
import Providers from "@/Components/providers/Providers";
import MetaProvider from "@/Components/providers/MetaProvider";
import I18nProvider from "@/providers/I18nProvider";
import { Toaster } from "react-hot-toast";
import "@/lib/useTranslation";
import { LanguageProvider } from "@/context/LanguageContext";
import MetaHead from "@/Seo/Meta/MetaHead";
import ClientSideCanonicalTag from "@/Components/ClientSideCanonicalTag";

export default function RootLayout({ children }) {
  return (
    <Providers>
      <MetaProvider>
        <I18nProvider>
          <LanguageProvider>
            <html lang="en">
              <head>
                <Script
                  src="https://cmp.osano.com/fT1j7UREwV/19bf3e9d-4e98-4a0a-86cc-abeacfca9b84/osano.js"
                  strategy="beforeInteractive"
                />
                <Suspense fallback={<></>}>
                  <ClientSideCanonicalTag />
                </Suspense>
              </head>
              <body
              // className={gilroy.variable}
              >
                <InactivityHandler />
                <Toaster
                  position="top-right"
                  reverseOrder={false}
                  toastOptions={{
                    style: {
                      zIndex: 99999,
                    },
                  }}
                />
                {children}
              </body>
            </html>
          </LanguageProvider>
        </I18nProvider>
      </MetaProvider>
    </Providers>
  );
}
