"use client";

import {Col, Container, Row} from "react-bootstrap";
import {PlusIcon, SolidRedArrowIcon,} from "@/assets/svgIcons/SvgIcon";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import CommonHead from "@/Components/common/Dashboard/CommonHead";
import CommonButton from "@/Components/UI/CommonButton";
import DashboardLayout from "@/Layouts/DashboardLayout";
import "@/css/dashboard/TradeBuilder.scss";
import TradeBuilderEntry from "@/Components/common/TradeBuilder/TradeBuilderEntry";
import TradeBuilderExit from "@/Components/common/TradeBuilder/TradeBuilderExit";
import MetaHead from "@/Seo/Meta/MetaHead";
import {get} from "@/utils/apiUtils";
import {React, useEffect, useState} from "react";

const TradeBuilder = () => {
  const [tradeForms, setTradeForms] = useState([{ id: 1, type: "entry" }]);
  const [databasePayload, setDatabasePayload] = useState({});

  const addEntryForm = () => {
    const newFormKey = `entry_${tradeForms.filter((f) => f.type === "entry").length + 1}`;
    setTradeForms([...tradeForms, { id: tradeForms.length + 1, type: "entry" }]);

    setFormData((prev) => ({
      ...prev,
      [newFormKey]: { ...tradeData.entry },
    }));
  };

  const addExitForm = () => {
    const newFormKey = `exit_${tradeForms.filter((f) => f.type === "exit").length + 1}`;
    setTradeForms([...tradeForms, { id: tradeForms.length + 1, type: "exit" }]);

    setFormData((prev) => ({
      ...prev,
      [newFormKey]: { ...tradeData.exit },
    }));
  };

  const updateDatabasePayload = (formKey, updatedFields) => {
  setDatabasePayload((prev) => ({
    ...prev,
    [formKey]: {
      ...prev[formKey],
      section: updatedFields.section,
      data: {
        ...prev[formKey]?.data,
        ...updatedFields.data,
      },
    },
  }));
};

  const [tradeData, setTradeData] = useState({
    entry: { overview: [], projection: [], outcome: [] },
    exit: { overview: [], projection: [], outcome: [] },
  });

  const [formData, setFormData] = useState({});

  const [transactionFields, setTransactionFields] = useState([]);
  const [tradeFields, setTradeFields] = useState([]);
  const [portfolioFields, setPortfolioFields] = useState([]);

  useEffect(() => {
    const fetchTradeData = async () => {
      try {
        const response = await get("/trade");
        const transformList = (list) =>
            list.map(
                ({
                   field_name: title,
                   summary: tooltip,
                   database_field: input,
                   account_field,
                   portfolioValue,
                   is_editable,
                   datatype,
                   metric_dimension,
                   expected_values,
                   has_formula
                 }) => ({
                  title,
                  tooltip,
                  input,
                  account_field,
                  portfolioValue,
                  ...(is_editable !== undefined && { is_editable }),
                  datatype,
                  metric_dimension,
                  expected_values: Array.isArray(expected_values) ? expected_values : [],
                  has_formula
                })
            );

        const entryOverview = transformList(response?.entry_overview || []);
        const entryProjection = transformList(response?.entry_projection || []);
        const entryOutcome = transformList(response?.entry_outcome || []);

        const exitOverview = transformList(response?.exit_overview || []);
        const exitProjection = transformList(response?.exit_projection || []);
        const exitOutcome = transformList(response?.exit_outcome || []);

        setTradeData({
          entry: { overview: entryOverview, projection: entryProjection, outcome: entryOutcome },
          exit: { overview: exitOverview, projection: exitProjection, outcome: exitOutcome },
        });

        setFormData({
          entry_1: { overview: entryOverview, projection: entryProjection, outcome: entryOutcome },
        });
      } catch (error) {
        console.error("Error fetching trade data:", error);
      }
    };

    fetchTradeData();
  }, []);

  useEffect(() => {
    const fetchDynamicFields = async () => {
      try {
        const response = await get("/trade/fetch/fields");

        setTransactionFields(response.transactions || []);
        setTradeFields(response.trades || []);
        setPortfolioFields(response.portfolios || []);
      } catch (error) {
        console.error("Error fetching dynamic fields:", error);
      }
    };

    fetchDynamicFields();
  }, []);

  const saveTradeData = () => {
    console.log("Data saved!", databasePayload);
  };

  const entrydata = [
    {
      tarde: "EXIT 2",
      entrydate: "Entry Date",
      ticker: "Ticker",
      entryprice: "Entry Price",
      exitprice: "Exit Price",
      position: "Position",
      pl: "P&L$",
    },
    {
      tarde: "EXIT 1",
      entrydate: "Entry Date",
      ticker: "Ticker",
      entryprice: "Entry Price",
      exitprice: "Exit Price",
      position: "Position",
      pl: "P&L$",
    },
  ];

  const metaArray = {
    noindex: true,
    title: "Trade Builder | Manually Build Your Trades | TradeReply",
    description:
      "Manually build and customize your trades with TradeReply's Trade Builder. Create precise trades tailored to your strategy.",
    canonical_link: "https://www.tradereply.com/dashboard/trade-builder",
    og_site_name: "TradeReply",
    og_title: "Trade Builder | Manually Build Your Trades | TradeReply",
    og_description:
      "Manually build and customize your trades with TradeReply's Trade Builder. Create precise trades tailored to your strategy.",
    twitter_title: "Trade Builder | Manually Build Your Trades | TradeReply",
    twitter_description:
      "Manually build and customize your trades with TradeReply's Trade Builder. Create precise trades tailored to your strategy.",
  };

  return (
    <DashboardLayout>
      <MetaHead props={metaArray} />
      <div className="trade_manager trade_builder">
        <CommonHead isShowCalender="false" />
        <Container>
          <Row className="trade_head align-items-center pt-4 pb-6">
            <Col md={4} xs={12} className="order-md-1">
              <AdminHeading heading="Trade Builder" centered />
            </Col>
            <Col md={4} xs={12}>
              <div className="trade_head_title">
                <h4>Draft 3</h4>
              </div>
            </Col>
            <Col md={4} xs={12} className="order-last">
              <div className="trade_head_btns d-sm-flex">
                <CommonButton title="Delete" className="red-btn me-2" />
                <CommonButton
                  title="Publish"
                  className="green-btn"
                  onClick={saveTradeData}
                />
              </div>
            </Col>
          </Row>
          {tradeForms.map((form, index) =>
            (() => {
              const formNumber =
                tradeForms.slice(0, index).filter((f) => f.type === form.type)
                  .length + 1;

              const formKey = `${form.type}_${formNumber}`;
              return (
                <div
                  key={form.id}
                  className={`trade_builder_card mb-4 ${form.type === "exit" ? "greengrandientbg" : ""
                    }`}
                >
                  {form.type === "entry" ? (
                      <TradeBuilderEntry
                          formKey={formKey}
                          formData={formData}
                          setFormData={setFormData}
                          transactionFields={transactionFields}
                          tradeFields={tradeFields}
                          portfolioFields={portfolioFields}
                          updateDatabasePayload={updateDatabasePayload}
                      />
                  ) : (
                    <TradeBuilderExit
                      formKey={formKey}
                      formData={formData}
                      index={formNumber}
                      setFormData={setFormData}
                      transactionFields={transactionFields}
                      tradeFields={tradeFields}
                      portfolioFields={portfolioFields}
                      updateDatabasePayload={updateDatabasePayload}
                    />
                  )}
                </div>
              );
            })()
          )}
          <div className="trade_manager_btns my-30">
            <Row>
              <Col lg={6} xs={12}>
                <CommonButton
                  title="Add Entry"
                  onlyIcon={<PlusIcon />}
                  className="w-100"
                  onClick={addEntryForm}
                />
              </Col>
              <Col lg={6} xs={12}>
                <CommonButton
                  title="Add Exit"
                  onlyIcon={<PlusIcon />}
                  className="w-100"
                  onClick={addExitForm}
                />
              </Col>
            </Row>
          </div>
          <div className="trade_manager_trade_entry mt-30">
            {entrydata.map((item, index) => (
              <div
                key={index}
                className={`trade_manager_trade_entry_box Redgrandient ${index === 1 ? "greengrandient" : ""
                  }`}
              >
                <span className="solidArrow red_arrow me-3">
                  <SolidRedArrowIcon />
                </span>
                <div className="d-flex trade_manager_trade_entry_box_headtext align-items-center w-100 justify-content-between">
                  <h5>{item?.tarde}</h5>
                  <h5>{item?.entrydate}</h5>
                  <h5>{item?.exiddate}</h5>
                  <h5>{item?.ticker}</h5>
                  <h5>{item?.entryprice}</h5>
                  <h5>{item?.exitprice}</h5>
                  <h5>{item?.position}</h5>
                  <h5>{item?.pl}</h5>
                </div>
                <span className="solidArrow red_arrow endArrow ms-3">
                  <SolidRedArrowIcon />
                </span>
              </div>
            ))}
          </div>
        </Container>
      </div>
    </DashboardLayout>
  );
};

export default TradeBuilder;
