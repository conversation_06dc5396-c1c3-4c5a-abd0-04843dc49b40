"use client";

import { Col, Container, Row } from "react-bootstrap";
import "@/css/dashboard/TradeAnalysis.scss";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import CommonHead from "@/Components/common/Dashboard/CommonHead";
import MiniTabs from "@/Components/common/TradeAnalysis/MiniTabs";
import DashboardLayout from "@/Layouts/DashboardLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import { useState, React } from "react";
import ConfigureTab from "@/Components/common/TradeAnalysis/ConfigureTab";
import SetupTab from "@/Components/common/TradeAnalysis/SetupTab";
import ViewTab from "@/Components/common/TradeAnalysis/ViewTab";

const TradeBuilder = () => {
  const [activeTab, setActiveTab] = useState('Setup');
  const addActiveTab = (active) => {
    setActiveTab(active)

  }
  const metaArray = {
    noindex: true,
    title: "Trade Analysis | TradeReply",
    description:
      "Manually build and customize your trades with TradeReply's Trade Builder. Create precise trades tailored to your strategy.",
    canonical_link: "https://www.tradereply.com/dashboard/trade-builder",
    og_site_name: "TradeReply",
    og_title: "Trade Analysis | TradeReply",
    og_description:
      "Manually build and customize your trades with TradeReply's Trade Builder. Create precise trades tailored to your strategy.",
    twitter_title: "Trade Analysis | TradeReply",
    twitter_description:
      "Manually build and customize your trades with TradeReply's Trade Builder. Create precise trades tailored to your strategy.",
  };

  return (
    <DashboardLayout>
      <MetaHead props={metaArray} />
      <div className="trade_manager trade_builder">
        <CommonHead isShowCalender={false} />
        <Container>
          <div className="trade_head">
            <AdminHeading heading="Trade Analysis" />
          </div>
          <MiniTabs
            onClick={addActiveTab}
            activeTab={activeTab}
          />
          {activeTab == 'Configure' && (
            <ConfigureTab />
          )}
          {activeTab == 'Setup' && (
            <SetupTab />
          )}
          {activeTab == 'View' && (
            <ViewTab />
          )}
        </Container>
      </div>
    </DashboardLayout>
  );
};

export default TradeBuilder;
