import React, { useState } from "react";
import { SolidInfoIcon } from "@/assets/svgIcons/SvgIcon";
import CommonTooltip from "@/Components/UI/CommonTooltip";

export default function ScopeTabContent({
  activeTab,
  setActiveTab,
  dimensionData,
  metricsData,
  btnIncExc,
  onSelectField,
  selectField
})
{
  const [selectedField, setSelectedField] = useState({
    id: selectField?.id || '',
    title: selectField?.title || '',
  });
  const handleSelect = (item) => {
      onSelectField(item);
      setSelectedField({
        id: item.id,
        title: item.title,
      });
    };
  return (
    <div>
      <div className="">
        <div className="left_side">
          <div
            className={`scope_dimension ${
              activeTab === "dimension" ? "active" : ""
            }`}
            onClick={() => setActiveTab("dimension")}
          >
            <p>Dimensions</p>
            <span className="scopeCount">120</span>
          </div>
          <div
            className={`scope_metrices ${
              activeTab === "metrics" ? "active" : ""
            }`}
            onClick={() => setActiveTab("metrics")}
          >
            <p>Metrics</p>
            <span className="scopeCount">359</span>
          </div>
        </div>

        <div className="right_side">
          {activeTab === "dimension" &&
            dimensionData.map((item, index) =>
              !btnIncExc ? (
                <label
                  className={`scope_dimension_show`}
                  key={index}
                  htmlFor={`selectDimension-${index}`}
                >
                  <input
                    className="custom_checkbox_input form-check-input"
                    type="checkbox"
                    id={`selectDimension-${index}`}
                    style={{ pointerEvents: "auto" }}
                  />
                  <span
                    className="name custom_checkbox_label ps-1"
                  >
                    {item.title}
                  </span>
                  <CommonTooltip
                    className="CustomTooltip"
                    content="💡 These are portfolio-level settings. Some are required to submit trades and will."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </label>
              ) : (
                <div className={`scope_dimension_show ${selectedField.id==item.id ? 'active_row' : ''}`} key={index} onClick={() => handleSelect(item)}>
                  <p className="name custom_checkbox_label ps-1">{item.title}</p>
                  <CommonTooltip
                    className="CustomTooltip"
                    content="💡 These are portfolio-level settings. Some are required to submit trades and will."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
              )
            )}

          {activeTab === "metrics" &&
            metricsData.map((item, index) =>
              !btnIncExc ? (
                <label 
                  className="scope_dimension_show" 
                  key={index}
                  htmlFor={`selectDimension-${index}`}
                >
                  <input
                    className="custom_checkbox_input form-check-input"
                    type="checkbox"
                    id={`selectDimension-${index}`}
                    style={{ pointerEvents: "auto" }}
                  />
                  <span
                    className="name custom_checkbox_label ps-1"
                  >
                    {item.title}
                  </span>
                  <CommonTooltip
                    className="CustomTooltip"
                    content="💡 These are portfolio-level settings. Some are required to submit trades and will."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </label>
              ) : (
                <div className={`scope_dimension_show ${selectedField.id == item.id ? 'bg-light' : ''}`}  key={index}  onClick={() => handleSelect(item)}>
                  <p className="name custom_checkbox_label ps-1">{item.title}</p>
                  <CommonTooltip
                    className="CustomTooltip"
                    content="💡 These are portfolio-level settings. Some are required to submit trades and will."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
              )
            )}
        </div>
      </div>
    </div>
  );
}
