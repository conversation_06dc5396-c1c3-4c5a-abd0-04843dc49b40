import { useState, useEffect, useRef } from "react";
import { Col, Row } from "react-bootstrap";
import {
  DropArrowIcon,
  SolidInfoIcon,
  PlusIcon,
  DropArrowUpIcon,
  MinusIcon,
} from "@/assets/svgIcons/SvgIcon";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import { post } from "@/utils/apiUtils";
import CommonButton from "@/Components/UI/CommonButton";
import ConfigScopeModal from "./ConfigScopeModal";

export default function TradeBuilderEntry({
  formKey,
  formData,
  setFormData,
  transactionFields,
  tradeFields,
  portfolioFields,
  updateDatabasePayload,
}) {
  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;
  const [inputValues, setInputValues] = useState({});
  const [lockedFields, setLockedFields] = useState({});
  const [touchedFields, setTouchedFields] = useState({});
  const [projectionValues, setProjectionValues] = useState({});
  const [outcomeValues, setOutcomeValues] = useState({});
  const [blurredFields, setBlurredFields] = useState({});
  const [isDimentionModal, setIsDimentionModal] = useState(false);
  const [entryIsOpen, setEntryIsOpen] = useState(false);
  const [height, setHeight] = useState("0px");
  const contentRef = useRef(null);
  const [activeSection, setActiveSection] = useState("overview");
  const [inputMode, setInputMode] = useState({
    transaction_risk_percentage: true,
    transaction_quantity_purchased: false,
  });
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pendingToggleField, setPendingToggleField] = useState(null);
  const [notesData, setNotesData] = useState("");
  const [updatedCalculatedFields, setUpdatedCalculatedFields] = useState({});

  const overviewList = Array.isArray(formData[formKey]?.overview)
    ? formData[formKey].overview
    : [];
  const entryProjectionList = Array.isArray(formData[formKey]?.projection)
    ? formData[formKey].projection
    : [];
  const entryOutcomeList = Array.isArray(formData[formKey]?.outcome)
    ? formData[formKey].outcome
    : [];

  const openDimentionModal = () => setIsDimentionModal(true);
  const closeDimentionModal = () => setIsDimentionModal(false);

  const toggleEntryCollapse = () => {
    setEntryIsOpen((prev) => !prev);
  };

  useEffect(() => {
    if (entryIsOpen) {
      // setHeight(`${contentRef.current.scrollHeight}px`);
      setHeight("auto");
    } else {
      setHeight("0px");
    }
  }, [entryIsOpen]);

  useEffect(() => {
    // Preserve existing user-entered values and initialize new fields only
    setInputValues((prev) => {
      const newValues = { ...prev };
      overviewList.forEach((item) => {
        if (!(item.input in newValues)) {
          newValues[item.input] = item?.portfolioValue ?? "";
        }
        setInputMode((prev) => ({
          ...prev,
          [item.input]: item.is_editable || false,
        }));
      });
      return newValues;
    });

    setLockedFields((prev) => {
      const newLocks = { ...prev };
      overviewList.forEach((item) => {
        if (!(item.input in newLocks)) {
          newLocks[item.input] = false;
        }
        setInputMode((prev) => ({
          ...prev,
          [item.input]: item.is_editable || false,
        }));
      });
      return newLocks;
    });

    setTouchedFields((prev) => {
      const newTouched = { ...prev };
      overviewList.forEach((item) => {
        if (!(item.input in newTouched)) {
          newTouched[item.input] = false;
        }
        setInputMode((prev) => ({
          ...prev,
          [item.input]: item.is_editable || false,
        }));
      });
      return newTouched;
    });

    setProjectionValues((prev) => {
      const newValues = { ...prev };
      entryProjectionList.forEach((item) => {
        if (!(item.input in newValues)) {
          newValues[item.input] = item?.portfolioValue ?? "";
        }
        setInputMode((prev) => ({
          ...prev,
          [item.input]: item.is_editable || false,
        }));
      });
      return newValues;
    });

    setOutcomeValues((prev) => {
      const newValues = { ...prev };
      entryOutcomeList.forEach((item) => {
        if (!(item.input in newValues)) {
          newValues[item.input] = item?.portfolioValue ?? "";
        }
        setInputMode((prev) => ({
          ...prev,
          [item.input]: item.is_editable || false,
        }));
      });
      return newValues;
    });
  }, [formKey, overviewList, entryProjectionList, entryOutcomeList]);

  const toggleLock = (field) => {
    setLockedFields((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const confirmToggleInputMode = (field) => {
    const isSpecial =
      field === "transaction_risk_percentage" ||
      field === "transaction_quantity_purchased";

    if (!isSpecial) {
      toggleInputMode(field); // Toggle directly
    } else {
      setPendingToggleField(field);
      setShowConfirmModal(true);
    }
  };

  const handleConfirmToggle = () => {
    if (pendingToggleField) {
      toggleInputMode(pendingToggleField);
      setPendingToggleField(null);
      setShowConfirmModal(false);
    }
  };

  const handleCancelToggle = () => {
    setPendingToggleField(null);
    setShowConfirmModal(false);
  };
  const toggleInputMode = (field) => {
    setInputMode((prev) => {
      const isCurrentlyManual = prev[field];
      const newValue = !isCurrentlyManual;

      const updated = {
        ...prev,
        [field]: newValue,
      };

      if (field === "transaction_risk_percentage") {
        updated["transaction_quantity_purchased"] = !newValue;
      } else if (field === "transaction_quantity_purchased") {
        updated["transaction_risk_percentage"] = !newValue;
      }

      return updated;
    });
  };

  const formatValue = (value, datatype, isBlurred) => {
    if (value === null || value === undefined) return 0;

    if (datatype?.toLowerCase().includes("date")) {
      const date = new Date(value);
      if (!isNaN(date.getTime())) return date.toISOString().slice(0, 10);
      return value;
    }

    if (datatype?.toLowerCase().includes("time")) {
      const date = new Date(value);
      if (!isNaN(date.getTime())) return date.toISOString().slice(11, 19);
      return value;
    }

    const num = parseFloat(value);
    if (isNaN(num)) return value;
    if (!datatype || !isBlurred) return num;

    const lower = datatype.toLowerCase();
    if (lower.includes("percentage")) return `${num}%`;
    if (
      lower.includes("currency") ||
      lower.includes("usd") ||
      lower.includes("$")
    )
      return `$${num}`;
    if (lower.includes("number")) return num;
    return value;
  };

  const handleBlur = (field) => {
    setBlurredFields((prev) => ({
      ...prev,
      [field]: true,
    }));
  };

  const handleFocus = (field) => {
    setBlurredFields((prev) => ({
      ...prev,
      [field]: false,
    }));
  };

  const handleAddFields = (newFields) => {
    const transformList = (list) =>
      list.map(
        ({
          field_name: title,
          summary: tooltip,
          database_field: input,
          account_field,
          portfolioValue,
          is_editable,
          datatype,
          metric_dimension,
          expected_values,
          has_formula,
        }) => ({
          title,
          tooltip,
          input,
          account_field,
          portfolioValue,
          ...(is_editable !== undefined && { is_editable }),
          datatype,
          metric_dimension,
          show_icon: true,
          expected_values: Array.isArray(expected_values)
            ? expected_values
            : [],
          has_formula,
        })
      );

    const transformedNewFields = transformList(newFields);

    setFormData((prevFormData) => {
      const currentList = Array.isArray(prevFormData[formKey]?.[activeSection])
        ? prevFormData[formKey][activeSection]
        : [];
      const existingInputs = new Set(
        currentList.map((item) => item.input || item.database_field)
      );
      const uniqueFields = transformedNewFields.filter(
        (field) => !existingInputs.has(field.input)
      );

      return {
        ...prevFormData,
        [formKey]: {
          ...prevFormData[formKey],
          [activeSection]: [...currentList, ...uniqueFields],
        },
      };
    });

    // Update inputMode for new fields
    newFields.forEach(({ database_field, is_editable }) => {
      setInputMode((prev) => ({
        ...prev,
        [database_field]: is_editable || false,
      }));
    });

    // Set values for new fields from updatedCalculatedFields if available
    if (activeSection === "overview") {
      setInputValues((prev) => {
        const newValues = { ...prev };
        transformedNewFields.forEach((field) => {
          const fieldKey = field.input.toUpperCase();
          if (!(field.input in newValues)) {
            newValues[field.input] =
              fieldKey in updatedCalculatedFields
                ? updatedCalculatedFields[fieldKey]
                : field.portfolioValue ?? "";
          }
        });
        return newValues;
      });
    } else if (activeSection === "projection") {
      setProjectionValues((prev) => {
        const newValues = { ...prev };
        transformedNewFields.forEach((field) => {
          const fieldKey = field.input.toLowerCase();
          if (!(field.input in newValues)) {
            newValues[field.input] =
              fieldKey in updatedCalculatedFields
                ? updatedCalculatedFields[fieldKey]
                : field.portfolioValue ?? "";
          }
        });
        return newValues;
      });
    } else if (activeSection === "outcome") {
      setOutcomeValues((prev) => {
        const newValues = { ...prev };
        transformedNewFields.forEach((field) => {
          const fieldKey = field.input.toLowerCase();
          if (!(field.input in newValues)) {
            newValues[field.input] =
              fieldKey in updatedCalculatedFields
                ? updatedCalculatedFields[fieldKey]
                : field.portfolioValue ?? "";
          }
        });
        return newValues;
      });
    }
  };

  const handleInputChange = async (e, databaseField, expectedValues = []) => {
    const newValue = e.target.value;

    const isDropdown = expectedValues.length > 0;
    if (!isDropdown && newValue !== "" && !/^\d+(\.\d*)?$/.test(newValue))
      return;

    const updatedInputValues = { ...inputValues };
    const updatedProjectionValues = { ...projectionValues };
    const updatedOutcomeValues = { ...outcomeValues };

    if (updatedInputValues.hasOwnProperty(databaseField)) {
      updatedInputValues[databaseField] = newValue;
    }
    if (updatedProjectionValues.hasOwnProperty(databaseField)) {
      updatedProjectionValues[databaseField] = newValue;
    }
    if (updatedOutcomeValues.hasOwnProperty(databaseField)) {
      updatedOutcomeValues[databaseField] = newValue;
    }

    const allInputs = {
      ...updatedInputValues,
      ...updatedProjectionValues,
      ...updatedOutcomeValues,
    };

    const formulaModeInputs = {};
    Object.entries(allInputs).forEach(([key, value]) => {
      formulaModeInputs[key] = inputMode[key] === false;
    });

    setInputValues(updatedInputValues);
    setProjectionValues(updatedProjectionValues);
    setOutcomeValues(updatedOutcomeValues);

    setTouchedFields((prev) => ({
      ...prev,
      [databaseField]: true,
    }));

    let section = "";
    if (overviewList.some((item) => item.input === databaseField)) {
      section = "overview";
    } else if (
      entryProjectionList.some((item) => item.input === databaseField)
    ) {
      section = "projection";
    } else if (entryOutcomeList.some((item) => item.input === databaseField)) {
      section = "outcome";
    }

    try {
      const response = await post("/trade/calculate", {
        inputs: allInputs,
        locked: lockedFields,
        changedField: databaseField,
        formulaModeInputs,
      });

      const updatedFields = response?.calculated || {};

      setUpdatedCalculatedFields((prev) => ({
        ...prev,
        ...updatedFields,
      }));

      Object.entries(updatedFields).forEach(([key, value]) => {
        const lowerKey = key.toLowerCase();
        if (lowerKey in updatedInputValues)
          updatedInputValues[lowerKey] = value;
        if (lowerKey in updatedProjectionValues)
          updatedProjectionValues[lowerKey] = value;
        if (lowerKey in updatedOutcomeValues)
          updatedOutcomeValues[lowerKey] = value;
      });

      setInputValues(updatedInputValues);
      setProjectionValues(updatedProjectionValues);
      setOutcomeValues(updatedOutcomeValues);

      updateDatabasePayload(formKey, {
        section: section,
        data: {
          ...updatedFields,
          [databaseField.toUpperCase()]: newValue,
        },
      });
    } catch (error) {
      console.error("Error calling /trade/calculate:", error);
    }
  };
  const handleMinusClick = (itemToRemove) => {
    setFormData((prevFormData) => {
      const currentList = Array.isArray(prevFormData[formKey]?.[activeSection])
        ? prevFormData[formKey][activeSection]
        : [];

      const updatedList = currentList.filter(
        (item) => item.input !== itemToRemove.input
      );

      return {
        ...prevFormData,
        [formKey]: {
          ...prevFormData[formKey],
          [activeSection]: updatedList,
        },
      };
    });
  };

  const MAX_NOTES_LENGTH = 500;
  const notesRef = useRef(null);

  const autoResize = (ref) => {
    if (ref.current) {
      ref.current.style.height = "auto";
      ref.current.style.height = `${ref.current.scrollHeight}px`;
    }
  };
  useEffect(() => {
    autoResize(notesRef);
  }, [notesData]);
  useEffect(() => {
    const handleResize = () => {
      autoResize(notesRef);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const renderList = (list, values) => (
    <>
      <Row className="g-3 align-items-stretch">
        {list.map((item, index) => (
          <Col key={item.input} md={4} sm={6} xs={12} className="d-flex">
            <div className="trade_builder_card_body_box d-flex flex-column w-100">
              {/* Header */}
              <div className="head h-25 flex-grow-1 d-flex flex-column">
                <div className="d-flex align-items-center gap-2 w-100">
                  <CommonTooltip
                    className="subTooltip"
                    content={
                      <>
                        {item?.tooltip}
                        {item?.input && (
                          <>
                            {" "}
                            <a
                              href={`/education/${item.input
                                .replace(/^portfolio_|^transaction_/, "")
                                .replace(/_/g, "-")}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              style={{
                                color: "#00bfff",
                                textDecoration: "underline",
                              }}
                            >
                              Read more
                            </a>
                          </>
                        )}
                      </>
                    }
                    position="top-left"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                  <h5 className="w-100">{item?.title}</h5>
                  <div className="d-flex align-items-center justify-end">
                    {item.is_editable &&
                      item.has_formula &&
                      item.account_field !== "YES" && (
                        <label className="switch">
                          <input
                            type="checkbox"
                            checked={inputMode[item.input] === false}
                            onChange={() => confirmToggleInputMode(item.input)}
                          />
                          <span className="slider round"></span>
                        </label>
                      )}
                    {item.show_icon && (
                      <div
                        className="d-flex align-items-center justify-content-center"
                        style={{
                          height: "22px",
                          width: "25px",
                          cursor: "pointer",
                        }}
                        onClick={() => handleMinusClick(item)}
                      >
                        <MinusIcon height="15px" width="15px" />
                      </div>
                    )}
                  </div>
                </div>
              </div>
              {/* Type Labels */}
              <div className="show_metrics_dimenstion">
                <span>
                  {item.input?.toLowerCase().startsWith("transaction_")
                    ? "TRANSACTION:"
                    : item.input?.toLowerCase().startsWith("trade_")
                    ? "TRADE:"
                    : item.input?.toLowerCase().startsWith("portfolio_")
                    ? "PORTFOLIO:"
                    : ""}
                </span>
                <span>
                  {item.metric_dimension === "dimension"
                    ? "DIMENSION"
                    : "METRIC"}
                </span>
              </div>

              {/* Input Section (stretched to fill remaining height) */}
              <div className="d-flex flex-column justify-content-end flex-grow-1">
                {item.is_editable &&
                item?.expected_values?.length > 0 &&
                item.input !== "transaction_leverage_factor" &&
                item.account_field !== "YES" ? (
                  <select
                    value={values[item.input] || ""}
                    onChange={(e) =>
                      handleInputChange(e, item.input, item.expected_values)
                    }
                    className="dropdown-select w-100"
                    style={{
                      backgroundColor: item.is_editable ? "white" : "black",
                      color: item.is_editable ? "black" : "white",
                    }}
                    disabled={!item.is_editable || lockedFields[item.input]}
                  >
                    <option value="">Select</option>
                    {item.expected_values.map((val) => (
                      <option key={val} value={val}>
                        {val}
                      </option>
                    ))}
                  </select>
                ) : (
                  <div className="input-container flex-grow-1 position-relative d-flex flex-column">
                    <input
                      type="text"
                      className="input-field flex-grow-1"
                      value={
                        item?.is_editable
                          ? formatValue(
                              values[item.input],
                              item?.datatype,
                              blurredFields[item.input]
                            )
                          : item.account_field === "YES"
                          ? formatValue(
                              item?.portfolioValue,
                              item?.datatype,
                              true
                            )
                          : formatValue(
                              values[item.input],
                              item?.datatype,
                              blurredFields[item.input]
                            )
                      }
                      style={{
                        backgroundColor:
                          !item.is_editable || item.account_field === "YES"
                            ? "#004080"
                            : inputMode[item.input] !== false
                            ? "white"
                            : "#004080",
                        color:
                          !item.is_editable || item.account_field === "YES"
                            ? "white"
                            : inputMode[item.input] !== false
                            ? "black"
                            : "white",
                        paddingRight:
                          inputMode[item.input] === false ? "30px" : "12px",
                      }}
                      onChange={(e) =>
                        handleInputChange(e, item.input, item.expected_values)
                      }
                      onBlur={() => handleBlur(item.input)}
                      onFocus={() => handleFocus(item.input)}
                      readOnly={
                        !item.is_editable ||
                        lockedFields[item.input] ||
                        inputMode[item.input] === false ||
                        item.account_field === "YES"
                      }
                    />
                    {(inputMode[item.input] === false ||
                      (item.has_formula && !item.is_editable)) && (
                      <div
                        style={{
                          position: "absolute",
                          right: "10px",
                          top: "50%",
                          transform: "translateY(-50%)",
                        }}
                      >
                        <CommonTooltip
                          className="subTooltip"
                          content="💡 This field is calculated using TradeReply’s proprietary formula."
                          position="top-left"
                        >
                          <img
                            src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-formula-icon.svg"
                            alt="Formula"
                            className="formula-icon"
                            style={{
                              width: "14px",
                              height: "14px",
                              opacity: 0.7,
                            }}
                          />
                        </CommonTooltip>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </Col>
        ))}
      </Row>
    </>
  );

  return (
    <>
      <div>
        <div className="trade_builder_card_head">
          <AdminHeading heading="Entry" centered />
          <button
            type="button"
            className="trade_builder_card_head_btnArrow"
            onClick={() => setEntryIsOpen(!entryIsOpen)}
          >
            {entryIsOpen ? <DropArrowUpIcon /> : <DropArrowIcon />}
          </button>
        </div>
        {entryIsOpen && (
          <div
            className="trade_builder_card_body_wrapper"
            style={{
              height: height || "auto",
              overflow: "hidden",
              transition: "height 0.5s ease",
            }}
            ref={contentRef}
          >
            <div className="relative trade_builder_card_body_notes">
              <label className="form-textarea-label">Notes</label>
              <textarea
                className="form-textarea w-full resize-none overflow-hidden"
                rows="4"
                placeholder="Notes"
                ref={notesRef}
                maxLength={MAX_NOTES_LENGTH}
                value={notesData}
                onChange={(e) => {
                  const input = e.target.value;
                  const allowedCharactersRegex =
                    /^[a-zA-Z0-9\s.,:;!?'"()\-\n\r]*$/;

                  if (allowedCharactersRegex.test(input)) {
                    setNotesData(input);
                  }
                }}
              ></textarea>
              <div className="character-count">
                Characters left: {notesData.length}/{MAX_NOTES_LENGTH}
              </div>
            </div>
            <div className="trade_builder_card_body">
              {renderList(overviewList, inputValues)}
              <div className="trade_manager_btns">
                <CommonButton
                  title="Add Metric / Dimension"
                  onlyIcon={<PlusIcon />}
                  className="w-100 mt-3"
                  onClick={() => {
                    setActiveSection("overview");
                    openDimentionModal();
                  }}
                />
              </div>
              <div className="trade_builder_card_head mt-4">
                <AdminHeading heading="Overview (Projection)" centered />
              </div>
              <div className="trade_builder_card_body">
                {renderList(entryProjectionList, projectionValues)}
              </div>
              <div className="trade_manager_btns">
                <CommonButton
                  title="Add Metric / Dimension"
                  onlyIcon={<PlusIcon />}
                  className="w-100 mt-3"
                  onClick={() => {
                    setActiveSection("projection");
                    openDimentionModal();
                  }}
                />
              </div>
              <div className="trade_builder_card_head mt-4">
                <AdminHeading heading="Overview (Post Trade)" centered />
              </div>
              <div className="trade_builder_card_body">
                {renderList(entryOutcomeList, outcomeValues)}
              </div>
              <div className="trade_manager_btns">
                <CommonButton
                  title="Add Metric / Dimension"
                  onlyIcon={<PlusIcon />}
                  className="w-100 mt-3"
                  onClick={() => {
                    setActiveSection("outcome");
                    openDimentionModal();
                  }}
                />
              </div>
            </div>
          </div>
        )}
      </div>
       {showConfirmModal && (
        <div className="inset-0 z-50 modal_overlay">
          <div className="search_section h-auto">
            <h2 className="h4 font-semibold mb-4">
              Only one of these fields can be manually entered at a time.
            </h2>

            {pendingToggleField &&
              (() => {
                const isManual = !inputMode[pendingToggleField];
                const action = isManual ? "Manual Entry" : "Auto-Calculate";
                const oppositeAction = isManual
                  ? "Auto-Calculate"
                  : "Manual Entry";
                const fieldLabel =
                  pendingToggleField === "transaction_risk_percentage"
                    ? "Risk Percentage"
                    : "Quantity Purchased";
                const otherLabel =
                  pendingToggleField === "transaction_risk_percentage"
                    ? "Quantity Purchased"
                    : "Risk Percentage";

                return (
                  <p className="mb-4 h5 font-regular text-gray-600">
                    Toggling <strong>{fieldLabel}</strong> to{" "}
                    <strong>{action}</strong> will switch{" "}
                    <strong>{otherLabel}</strong> to{" "}
                    <strong>{oppositeAction}</strong>.
                  </p>
                );
              })()}

            <div className="flex justify-end gap-3 mt-3">
              <CommonButton
                title="Cancel"
                className="gray-btn p-2 rounded-4 lh-sm"
                onClick={handleCancelToggle}
              />
              <CommonButton
                title="Confirm"
                className="btn-primary p-2 rounded-4 lh-sm"
                 onClick={handleConfirmToggle}
              />
            </div>
          </div>
        </div>
      )}
      {isDimentionModal && (
        <ConfigScopeModal
          transactionFields={transactionFields}
          tradeFields={tradeFields}
          portfolioFields={portfolioFields}
          onClose={closeDimentionModal}
          onConfirm={handleAddFields}
        />
      )}
    </>
  );
}
