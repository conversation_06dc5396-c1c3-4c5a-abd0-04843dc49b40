import React, { useEffect, useRef, useState } from "react";
import { WhiteCrossIcon, SearchIcons, RightArrowIcon } from "@/assets/svgIcons/SvgIcon";
import ScopeTabContent from "./ScopeTabContent";
import CommonButton from "@/Components/UI/CommonButton";

export default function ConfigScopeModal({ onClose, transactionFields, tradeFields, portfolioFields, onConfirm }) {
  const [activeScope, setActiveScope] = useState("Transaction");
  const [transactionActiveTab, setTransactionActiveTab] = useState("dimension");
  const [tradeActiveTab, setTradeActiveTab] = useState("dimension");
  const [portfolioActiveTab, setPortfolioActiveTab] = useState("dimension");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedFieldsInModal, setSelectedFieldsInModal] = useState([]);
  const scopeContainerRef = useRef(null);
  const [showPrevArrow, setShowPrevArrow] = useState(false);
  const [showNextArrow, setShowNextArrow] = useState(false);

  const scopes = [
    { name: "Transaction", count: transactionFields?.length },
    { name: "Trade", count: tradeFields?.length },
    { name: "Portfolio", count: portfolioFields?.length },
  ];

  const filterFields = (fields) =>
      fields?.filter((field) =>
          field?.field_name?.toLowerCase().includes(searchTerm?.toLowerCase())
      );

  const transactionMetrics = filterFields(
      transactionFields?.filter((field) => field?.metric_dimension === "metric")
  );
  const transactionDimension = filterFields(
      transactionFields?.filter((field) => field?.metric_dimension === "dimension")
  );

  const tradeMetrics = filterFields(
      tradeFields?.filter((field) => field?.metric_dimension === "metric")
  );
  const tradeDimension = filterFields(
      tradeFields?.filter((field) => field?.metric_dimension === "dimension")
  );

  const portfolioMetrics = filterFields(
      portfolioFields?.filter((field) => field?.metric_dimension === "metric")
  );
  const portfolioDimension = filterFields(
      portfolioFields?.filter((field) => field?.metric_dimension === "dimension")
  );

  const checkScrollPosition = () => {
    const el = scopeContainerRef.current;
    if (!el) return;

    const { scrollLeft, clientWidth, scrollWidth } = el;

    setShowPrevArrow(scrollLeft > 5);
    setShowNextArrow(scrollLeft + clientWidth < scrollWidth - 5);
  };

  useEffect(() => {
    const el = scopeContainerRef.current;
    if (!el) return;

    checkScrollPosition();
    el.addEventListener("scroll", checkScrollPosition);
    window.addEventListener("resize", checkScrollPosition);

    return () => {
      el.removeEventListener("scroll", checkScrollPosition);
      window.removeEventListener("resize", checkScrollPosition);
    };
  }, []);

  const handleNextScroll = () => {
    scopeContainerRef.current?.scrollBy({ left: 100, behavior: "smooth" });
  };

  const handlePreviousScroll = () => {
    scopeContainerRef.current?.scrollBy({ left: -100, behavior: "smooth" });
  };

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm(selectedFieldsInModal, activeScope);
      onClose();
    }
  };

  return (
    <div className="modal_overlay">
      <div className="search_section">
        <div className="search_header_mobile d-xl-none d-block">
          <div className="d-flex justify-content-between align-items-center mb-3">
            <div className="d-flex align-items-center pe-4 gap-2">
              <div className="closeModal">
                <button onClick={onClose} >
                  <WhiteCrossIcon />
                </button>
              </div>
              <div className="d-md-flex gap-2 d-block">
                <p className="ms-md-2">Select Field</p>
                <p className="selected">19 of 367 selected</p>
              </div>
            </div>
            <CommonButton
              title="Confirm"
              className="btn-primary p-2 rounded-4 lh-sm"
            />
          </div>
          <div className="search py-3 mb-3">
            <SearchIcons />
            <input type="text" placeholder="Search metrics (Transaction scope)" />
          </div>
        </div>
        <div className="search_header d-none d-xl-flex">
          <div className="d-flex align-items-center pe-4 gap-2">
            <div className="closeModal">
              <button onClick={onClose} >
                <WhiteCrossIcon />
              </button>
            </div>
            <p className="ms-2">Select Field</p>
            <span className="selected">
              {selectedFieldsInModal.length} of{" "}
                {(() => {
                  if (activeScope === "Transaction") return transactionFields?.length || 0;
                  if (activeScope === "Trade") return tradeFields?.length || 0;
                  if (activeScope === "Portfolio") return portfolioFields?.length || 0;
                  return 0;
                })()}{" "}
              selected
          </span>
          </div>
          <div className="search">
            <SearchIcons />
            <input
                type="text"
                placeholder="Search metrics or dimensions"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <CommonButton
            title="Confirm"
            className="btn-primary p-2 rounded-4 lh-sm"
            onClick={() => {
              handleConfirm(selectedFieldsInModal);
            }}
          />
        </div>
        <div className="scope_section_wrapper">
          <div className="scope_section" ref={scopeContainerRef}>
            {showPrevArrow && (
              <div className="move-pre-arrow" onClick={handlePreviousScroll}>
                <div className="icon">
                  <RightArrowIcon />
                </div>
              </div>
            )}
            {scopes.map((scope) => (
              <div
                key={scope.name}
                className={`scopeName ${activeScope === scope.name ? "active" : ""}`}
                onClick={() => setActiveScope(scope.name)}
              >
                <p>{scope.name} (Scope)</p>
                <span className="scopeCount">{scope.count}</span>
              </div>
            ))}
            {showNextArrow && (
              <div className="move-next-arrow" onClick={handleNextScroll}>
                <div className="icon">
                  <RightArrowIcon />
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="scope_content">
          {activeScope === "Transaction" && (
            <ScopeTabContent
              scope="Transaction"
              activeTab={transactionActiveTab}
              setActiveTab={setTransactionActiveTab}
              dimensionData={transactionDimension}
              metricsData={transactionMetrics}
              selectedFields={selectedFieldsInModal}
              onSelectionChange={setSelectedFieldsInModal}
            />
          )}

          {activeScope === "Trade" && (
            <ScopeTabContent
              scope="Trade"
              activeTab={tradeActiveTab}
              setActiveTab={setTradeActiveTab}
              dimensionData={tradeDimension}
              metricsData={tradeMetrics}
              selectedFields={selectedFieldsInModal}
              onSelectionChange={setSelectedFieldsInModal}
            />
          )}

          {activeScope === "Portfolio" && (
            <ScopeTabContent
              scope="Portfolio"
              activeTab={portfolioActiveTab}
              setActiveTab={setPortfolioActiveTab}
              dimensionData={portfolioDimension}
              metricsData={portfolioMetrics}
              selectedFields={selectedFieldsInModal}
              onSelectionChange={setSelectedFieldsInModal}
            />
          )}
        </div>
      </div>
    </div>
  );
}