import React, { useEffect, useRef, useState } from "react";
import { SolidInfoIcon, RightArrowIcon } from "@/assets/svgIcons/SvgIcon";
import CommonTooltip from "@/Components/UI/CommonTooltip";

export default function ScopeTabContent({
  activeTab,
  setActiveTab,
  dimensionData,
  metricsData, onSelectionChange, selectedFields
}) {
  const scrollRef = useRef(null);
  const [showPrevArrow, setShowPrevArrow] = useState(false);
  const [showNextArrow, setShowNextArrow] = useState(false);

  const checkScroll = () => {
    const el = scrollRef.current;
    if (!el) return;
    const { scrollLeft, scrollWidth, clientWidth } = el;
    setShowPrevArrow(scrollLeft > 5);
    setShowNextArrow(scrollLeft + clientWidth < scrollWidth - 5);
  };

  useEffect(() => {
    const el = scrollRef.current;
    if (!el) return;

    checkScroll();
    el.addEventListener("scroll", checkScroll);
    window.addEventListener("resize", checkScroll);

    return () => {
      el.removeEventListener("scroll", checkScroll);
      window.removeEventListener("resize", checkScroll);
    };
  }, []);

  const scrollLeft = () => {
    scrollRef.current?.scrollBy({ left: -100, behavior: "smooth" });
  };

  const scrollRight = () => {
    scrollRef.current?.scrollBy({ left: 100, behavior: "smooth" });
  };

  return (
    <div>
      <div className="left_side">
        {showPrevArrow && (
          <div className="move-pre-arrow" onClick={scrollLeft}>
            <div className="icon">
              <RightArrowIcon />
            </div>
          </div>
        )}

        <div className="scrollable_tabs" ref={scrollRef}>
          <div
            className={`scope_dimension ${activeTab === "dimension" ? "active" : ""}`}
            onClick={() => setActiveTab("dimension")}
          >
            <p>Dimensions</p>
            <span className="scopeCount">{dimensionData?.length}</span>
          </div>
          <div
            className={`scope_metrices ${activeTab === "metrics" ? "active" : ""}`}
            onClick={() => setActiveTab("metrics")}
          >
            <p>Metrics</p>
            <span className="scopeCount">{metricsData?.length}</span>
          </div>
        </div>

        {showNextArrow && (
          <div className="move-next-arrow" onClick={scrollRight}>
            <div className="icon">
              <RightArrowIcon />
            </div>
          </div>
        )}
      </div>

      <div className="right_side">
        {activeTab === "dimension" &&
          dimensionData.map((item, index) => (
            <label
              className="scope_dimension_show"
              key={index}
              htmlFor={`selectDimension-${index}`}
            >
              <input
                  className="custom_checkbox_input form-check-input"
                  type="checkbox"
                  id={`selectDimension-${index}`}
                  checked={selectedFields.some((f) => f.database_field === item?.database_field)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      onSelectionChange([...selectedFields, item]);
                    } else {
                      onSelectionChange(
                          selectedFields.filter((f) => f.database_field !== item?.database_field)
                      );
                    }
                  }}
              />

              {item?.field_name}

              <CommonTooltip
                className="CustomTooltip"
                content={item?.summary}
                position="bottom-right"
              >
                <SolidInfoIcon />
              </CommonTooltip>
            </label>
          ))
        }

        {activeTab === "metrics" &&
          metricsData.map((item, index) => (
            <label
              className="scope_metrices_show"
              key={index}
              htmlFor={`selectDimension-${index}`}
            >
              <input
                  className="custom_checkbox_input form-check-input"
                  type="checkbox"
                  id={`selectDimension-${index}`}
                  checked={selectedFields.some((f) => f.database_field === item?.database_field)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      onSelectionChange([...selectedFields, item]);
                    } else {
                      onSelectionChange(
                          selectedFields.filter((f) => f.database_field !== item?.database_field)
                      );
                    }
                  }}
              />
              {item?.field_name}
              <CommonTooltip
                className="CustomTooltip"
                content={item?.summary}
                position="bottom-right"
              >
                <SolidInfoIcon />
              </CommonTooltip>
            </label>
          ))
        }
      </div>
    </div>
  );
}
