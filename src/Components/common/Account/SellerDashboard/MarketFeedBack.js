"use client";
import { Col, Row } from "react-bootstrap";
import React from "react";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import {
  ThumbDownIcon,
  ThumbUpIcon,
  RightArrowIcon,
  ProfileUserDarkIcon,
  RatingStarIcon,
} from "@/assets/svgIcons/SvgIcon";
import { usePathname } from "next/navigation";

export default function MarketFeedBack() {

  const pathname = usePathname()

  const linkProps = pathname !== '/account/seller-feedback'
    ? {
        Linktext: "View all",
        Linkicon: <RightArrowIcon />,
        link: "/account/seller-feedback",
      }
    : {};
  
  return (
    <>
          <CommonWhiteCard
            title="Marketplace Seller Feedback"
            {...linkProps}
            className="account_card"
          >
            <div className="account_card_marketplace">
              <div className="main_inform justify-between">
                <div className="d-flex gap-1">
                  <RatingStarIcon />
                  <RatingStarIcon />
                  <RatingStarIcon />
                  <RatingStarIcon />
                  <RatingStarIcon />
                  <span>201</span>
                </div>
                <span className="most_recent text-end">
                  Most recent reviews
                </span>
              </div>
              <div className="mini_card">
                <div className="main_inform">
                  <div className="profile_photo">
                    <ProfileUserDarkIcon />
                  </div>
                  <div className="main_inform justify-between w-100">
                    <div>
                      <p className="small_tag">Sarah</p>
                      <h6>Purchased courses & webinars totalling $1,100</h6>
                    </div>
                    <div>
                      <div className="d-flex gap-1">
                        <RatingStarIcon />
                        <RatingStarIcon />
                        <RatingStarIcon />
                      </div>
                      <p className="time">6 hours ago</p>
                    </div>
                  </div>
                </div>
                <h6 className="mini_sc_title">
                  Purchased courses & webinars totalling $1,100
                </h6>
                <div className="main_inform">
                  <ThumbUpIcon />
                  <p className="thumbs_text">
                    Aaron's stock courses were amazing. I feel like a crypto
                    expert!
                  </p>
                </div>
              </div>
              <div className="mini_card">
                <div className="main_inform">
                  <div className="profile_photo">
                    <ProfileUserDarkIcon />
                  </div>
                  <div className="main_inform justify-between w-100">
                    <div>
                      <p className="small_tag">Sarah</p>
                      <h6>Purchased courses & webinars totalling $1,100</h6>
                    </div>
                    <div>
                      <div className="d-flex gap-1">
                        <RatingStarIcon />
                        <RatingStarIcon />
                        <RatingStarIcon />
                      </div>
                      <p className="time">6 hours ago</p>
                    </div>
                  </div>
                </div>
                <h6 className="mini_sc_title">
                  Purchased courses & webinars totalling $1,100
                </h6>
                <div className="main_inform">
                  <ThumbDownIcon />
                  <p className="thumbs_text">
                    Aaron's stock courses were amazing. I feel like a crypto
                    expert!
                  </p>
                </div>
              </div>
            </div>
          </CommonWhiteCard>
    </>
  );
}
