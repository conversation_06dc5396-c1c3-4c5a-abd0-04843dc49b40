"use client";
import { Col, Row, Dropdown } from "react-bootstrap";
import React, { useState, useEffect, useRef } from "react";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import {
  TripleDotsMenu,
  RightArrowIcon,
  BlackShareIcon,
  StaticListingImg,
  EyeDarkIcon,
  DeleteDarkIcon,
  RenameIcon,
} from "@/assets/svgIcons/SvgIcon";

export default function AcoountOverview() {
  const containerRef = useRef(null);
  const [openIndex, setOpenIndex] = useState(false);

  // Close on outside click
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target)
      ) {
        // setOpenIndex(null);

        setOpenIndex(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const toggleDropdown = () => {
    console.log("Ahmad");

    // setOpenIndex((prev) => (prev === index ? null : index));
    setOpenIndex((prev) => !prev);
  };
  return (
    <CommonWhiteCard
      title="Active Listings"
      Linktext="View all"
      link="/account/your-listings"
      Linkicon={<RightArrowIcon />}
      className="account_card"
    >
      <div className="account_card_active_listing">
        <div className="main_inform justify-between">
          <p className="most_recent">3 of 15</p>
          <span className="most_recent text-end">Sorted: Recently Created</span>
        </div>
        <div className="mini_card">
          <div className="main_inform respon_sell_feedback">
            <div className="activeListing_photo">
              <StaticListingImg />
            </div>
            <div>
              <h6>Mastering the stock market</h6>
              <p className="inner_price_text">$11.95 - Listed on 1/19/25</p>
              <p className="inner_price_text">
                65 clicks on listing since listed
              </p>
            </div>
          </div>
          <div className="d-flex gap-2 justify-end relative" ref={containerRef}>
            <button className="round-border-btn" type="button">
              <BlackShareIcon />
              Share
            </button>
            <button
              className="rounded-border-btn px-3"
              type="button"
              onClick={toggleDropdown}
            >
              <TripleDotsMenu />
            </button>
            {openIndex && (
              <Dropdown.Menu
                show
                style={{
                  position: "absolute",
                  bottom: "125%",
                  right: 4,
                  zIndex: 1000,
                }}
              >
                <Dropdown.Item className="dropdownlist" eventKey="2">
                  <EyeDarkIcon /> <span>View Listing</span>
                </Dropdown.Item>
                <Dropdown.Item className="dropdownlist" eventKey="3">
                  <RenameIcon /> <span>Edit Listing</span>
                </Dropdown.Item>
                <Dropdown.Item className="dropdownlist" eventKey="4">
                  <DeleteDarkIcon /> <span>Delete Listing</span>
                </Dropdown.Item>
              </Dropdown.Menu>
            )}
          </div>
          <div className="d-flex gap-2 mt-3 justify-stretch">
            <button className="round-bluefill-btn w-50" type="button">
              End Listing
            </button>
            <button className="round-bluefill-btn w-50" type="button">
              Mark out of stock
            </button>
          </div>
        </div>
      </div>
    </CommonWhiteCard>
  );
}
