import { blogLimit, placeHolderImg } from "@/constants";
import "../../../css/Home/Blog.scss"; 
import Link from "next/link"; // Import Next.js Link
 

const RecentPost = ({ img, title, text, coinname=null, className, time=null, href, category=null }) => {
  
  return (
    <>
      <Link href={href} className={`recent_post ${className}`}>
        <div className="recent_post_img">
          <img src={img || placeHolderImg} 
          alt={title ? `Recent blog post: ${title}` : "TradeReply blog article thumbnail"}
           />
        </div>
        <div className="recent_post_content">
          <p>{category ?? "N/A"}</p>
          <small>{coinname}</small>
          {/* <h4>{title}</h4> */}
     
          {time && <span className="recent_post_time">{time}</span>}
        </div>
      </Link>
    </>
  );
};

export default RecentPost;


     {/* <p>{truncateContent(text, blogLimit)}</p> */}
          {/* <p>{text || "No Text Available"}</p> */}
          {/* <p dangerouslySetInnerHTML={{ __html: text }} />  */}