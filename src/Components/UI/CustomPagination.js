'use client';

import { Pagination } from "react-bootstrap";
import Link from "next/link";
import { RightArrowIcon } from '@/assets/svgIcons/SvgIcon';
import "@/css/common/CustomPagination.scss";
import { usePathname } from "next/navigation";
import { useSearchParams } from "next/navigation";



export default function CustomPagination({ links, onDataSend, useLinks = true, pageUrl = "blog", useDynamicParams = false }) {
  if (!links || links.total === 0) return null;
  const searchParams = useSearchParams()
  const pathname = usePathname();
  const totalPages = links.total;
  const currentPage = links.current_page;

  // Store correct total pages in cookies
  const isSearch = !!searchParams.get('key');
  const startPage = Math.max(1, Math.min(currentPage - 1, totalPages - 2));
  const visiblePages = Array.from({ length: Math.min(3, totalPages) }, (_, i) => startPage + i);
  const pathParts = pathname?.split('/').filter(Boolean); // Remove empty segments
  const PageLinkUrl = pathParts[0];

  const getLink = (page) => {
    if (useDynamicParams) {
      return page === 1 ? `/${pageUrl}` : `/${pageUrl}/page/${page}`;
    } else {
      const params = new URLSearchParams(searchParams.toString());
      params.set('page', page);
      return `/${PageLinkUrl}?${params.toString()}`;
    }
  };

  const renderPageItem = (page) => {
    return useLinks ? (
      <Link
        href={getLink(page)}
        className={`mx-2 ${page === currentPage ? 'active' : ''}`}
        key={page}
        onClick={(e) => {
        // e.preventDefault();
        onDataSend(page);
      }}
      >
        {page}
      </Link>
    ) : (
      <Link
        className={`mx-2 link ${page === currentPage ? 'active' : ''}`}
        key={page}
        href={getLink(page)}
        onClick={(e) => {
          // e.preventDefault();
          onDataSend(page);
        }}

      >
        {page}
      </Link>
    );
  };


  return (
    <div className="customPagination">
      <Pagination>
        {/* Previous Page */}
        {links.prev_page ? (
          <Pagination.Prev
            as={Link}

            href={getLink(currentPage - 1)}
            className="prevArrow"
            rel="prev"
            aria-label="Previous Page"
          onClick={() => {

            // e.preventDefault();
            onDataSend(currentPage - 1)
          }}

          >
            <RightArrowIcon />
          </Pagination.Prev>
        ) : (
          <Pagination.Prev disabled className="prevArrow">
            <RightArrowIcon />
          </Pagination.Prev>
        )}

        {/* First Page */}
        {!visiblePages.includes(1) && (
          <>
            {renderPageItem(1)}
            <span className="txt-blue">...</span>
          </>
        )}

        {/* Page Numbers */}
        {visiblePages.map(renderPageItem)}

        {/* Last Page */}
        {!visiblePages.includes(totalPages) && (
          <>
            <span className="txt-blue">...</span>
            {renderPageItem(totalPages)}
          </>
        )}

        {/* Next Page */}
        {links.next_page ? (
          <Pagination.Next
            as={Link}
            href={getLink(currentPage + 1)}
            className="nextArrow"
            rel="next"
            aria-label="Next Page"
          onClick={(e) => {
            // e.preventDefault();
            onDataSend(currentPage + 1);
          }}
          >
            <RightArrowIcon />
          </Pagination.Next>
        ) : (
          <Pagination.Next disabled className="nextArrow">
            <RightArrowIcon />
          </Pagination.Next>
        )}
      </Pagination>
    </div>
  );
}

