.brokers .tableless .table-responsive {
    max-height: 400px;
    clip-path: inset(0 round 2rem);
    overflow: hidden;
    position: relative;
}

.brokers .tableless .common_table {
    table-layout: fixed;
    width: 100%;
    border-collapse: collapse;
}
.brokers .tableless .common_table thead {
    background-color: #000;
    position: sticky;
    top: 0;
    z-index: 1;
    display: block;
    padding-right: 5px;
}
.tableless .common_table thead {
    border: none;
}
.table>thead {
    vertical-align: bottom;
}
.brokers .tableless .common_table thead tr, .brokers .tableless .common_table tbody tr {
    display: table;
    table-layout: fixed;
    width: 100%;
}
.tableless .common_table thead tr {
    position: sticky;
    top: 0;
}

.brokers .tableless .common_table th:nth-child(1), .brokers .tableless .common_table td:nth-child(1) {
    width: 25%;
}
.brokers .tableless .common_table th:nth-child(2) , .brokers .tableless .common_table td:nth-child(2){
    width: 35%;
}
.tableless .common_table thead tr th:first-child {
    border-top-left-radius: 2rem;
    border-left: 0;
}
.brokers .tableless .common_table thead tr th {
    position: sticky;
    top: 0;
    left: 0;
    z-index: 11;
}
.tableless .common_table thead tr th {
    background-color: #000;
    font-size: 1.25rem;
    font-weight: 600;
    color: #fff;
    border-radius: 0;
    border-top: 0;
}
.tableless .common_table tr th, .tableless .common_table tr td {
    padding: 1.6rem 1rem;
    white-space: nowrap;
    vertical-align: middle;
    border: 1px solid #34415B;
}
.brokers .tableless .common_table tbody {
    overflow-x: hidden;
    width: 100%;
    display: block;
    max-height: 300px;
    overflow-y: auto;
}
.table>tbody {
    vertical-align: inherit;
}
tbody, td, tfoot, th, thead, tr {
    border-color: inherit;
    border-style: solid;
    border-width: 0;
}
.tableless {
    border: 1px solid #00ADEF;
    border-radius: 2rem;
    position: relative;
    z-index: 1;
}

.tableless:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url(/assets/trade_grndbg-BtZolFgj.png);
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100%;
    border-radius: 2rem;
    z-index: -1;
}

    /* ::-webkit-scrollbar {
        width: 8px;
        border-radius: 1rem;
    }
  
  ::-webkit-scrollbar-thumb {
    background-color: #00adef;
    border-radius: 1rem;
  }
  
  @media screen and (max-width: 767px) {
    ::-webkit-scrollbar {
      width: 6px;
    }
  } */
  