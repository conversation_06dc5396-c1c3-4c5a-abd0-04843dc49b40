@use "../theme/_var.scss" as *;

.marketplace {
    padding: 5rem 0;

    &_inner {
        &_heading {
            border-bottom: 3px solid rgba(0, 173, 239, 0.3);
            margin-bottom: 30px;
            padding-bottom: 1.25rem;


            h4 {
                margin-bottom: 1.25rem;
                font-size: 1.65rem;
                line-height: 35px;
                font-weight: 600;
            }

            .breadcrumb {
                margin: 0;
            }
        }
    }

    &_heading {
        h1 {
            font-size: 3rem;
            font-weight: 800;

            @media (max-width: 1199px) {
                font-size: 2.5rem;
            }

            @media (max-width: 767px) {
                font-size: 1.5rem;
            }

            @media (max-width: 390px) {
                font-size: 1.30rem;
            }
        }
    }

    &_shopcart {
        margin: 30px 0;

        @media screen and (max-width: 767px) {
            flex-wrap: wrap;
        }

        .education_search {
            margin: 0 50px;
            width: 450px;

            @media screen and (max-width: 991px) {
                width: 350px;
                margin: 0 30px;
            }

            @media screen and (max-width: 767px) {
                width: 100%;
                margin: 0;
                padding-top: 1.25rem;
            }

            .commonSearch {
                max-width: 100%;
                width: 100%;
            }
        }

        &_btn {
            button {
                background-color: transparent;
                border: 0;
                font-weight: 600;
                font-size: 1.25rem;
                color: $white;
                transition: all ease-in-out 0.3s;

                svg {
                    margin-right: 0.625rem;
                }

                &:hover {
                    background-color: transparent;
                    color: $baseclr;
                }
            }
        }
    }

    .common_select {
        margin-bottom: 0;

        .select__control {
            padding: 0;
            border: 0;
            min-width: 80px;
            min-height: auto;
        }
    }

    &_products {
        &_sellerInfo {
            margin: 20px 0;
            border-top: 3px solid #00adef4d;

            @media (width <=767px) {
                border-top: none;
            }

            h4 {
                margin: 20px 0;
            }

            .sellerProfile {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 20px;

                img {
                    min-height: 50px;
                    max-height: 50px;
                    min-width: 50px;
                    max-width: 50px;
                    border-radius: 50px;
                }

                p {
                    font-size: 22px;
                    font-weight: 600;
                }
            }

            .sellerRating {
                display: flex;
                align-items: center;
                gap: 5px;
                margin-bottom: 20px;

                img {
                    height: 25px;
                }

                span {
                    font-size: 24px;
                    font-weight: 600;
                }
            }

            .sellerBtn {
                button {
                    width: 100%;
                    min-height: 50px;
                }
            }
        }

        &_filter {
            .accordion {
                border-radius: 0;

                &-item {
                    background-color: transparent;
                    border: 0;
                    border-bottom: 3px solid rgba(0, 173, 239, 0.2);
                    border-radius: 0;
                }

                &-button {
                    background-color: transparent;
                    border: 0;
                    color: $white;
                    font-weight: 600;
                    font-size: 1.25rem;
                    padding: 1.5rem 0;
                    text-transform: capitalize;
                    border-radius: 0;

                    @media screen and (max-width: 991px) {
                        font-size: 1rem;
                        padding: 1rem 0;
                    }

                    &:focus {
                        box-shadow: none;
                    }

                    &:not(.collapsed) {
                        box-shadow: none;
                        background-color: transparent;
                        color: $white;

                        &::after {
                            background-image: url("https://cdn.tradereply.com/dev/site-assets/icons/tradereply-minus.svg");
                            transform: none;
                        }
                    }

                    &::after {
                        background-image: url("https://cdn.tradereply.com/dev/site-assets/icons/tradereply-plus.svg");
                        transform: none;
                        background-position: center;
                    }
                }

                &-body {
                    padding: 1rem 0 1.25rem;

                    button {
                        background-color: transparent;
                        border: 0;
                        display: block;
                        color: $white;
                        font-size: 16px;
                        font-weight: 600;
                        line-height: 26px;
                        padding: 6px 0;
                        width: 100%;
                        transition: all ease-in-out 0.3s;
                        text-align: left;

                        &:hover {
                            color: $baseclr;
                        }
                    }
                }
            }
        }

        &_sort {
            @media screen and (max-width: 991px) {
                justify-content: flex-end;
            }

            h5 {
                font-weight: 500;
                margin-right: 5px;
            }

            .common_select {

                .select__control {
                    min-width: 150px;
                }
            }
        }

        &_card {
            margin-top: 30px;

            &_rating {
                display: flex;
                align-items: center;
                gap: 5px;
                margin-bottom: 20px;

                img {
                    height: 25px;
                }

                span {
                    font-size: 24px;
                    font-weight: 600;
                    color: #fff;
                }
            }

            &_content {
                h4 {
                    @media screen and (max-width: 991px) {
                        font-size: 20px;
                        line-height: 30px;
                    }

                    @media screen and (max-width: 767px) {
                        font-size: 16px;
                        line-height: 24px;
                    }
                }
            }

            &_img {
                position: relative;

                &::after {
                    content: '';
                    // position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    padding-top: 90%;
                    display: block;
                }

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    position: absolute;
                    border-radius: 20px;
                }
            }
        }
    }

    .custom_breadcrumb {
        .home-item {
            display: none;
        }

        .secondary_link {
            padding: 0;

            &::before {
                display: none;
            }
        }
    }
}