
.form-check-input {
    width: 72px !important; 
    height: 34px !important; 
    border-radius: 30px !important; 
    position: relative;
    background-color: #fff !important; 
    border: none !important;
    cursor: pointer;
  }
  
//   .form-check-input::before {
//     content: "";
//     position: absolute;
//     width: 26px; 
//     height: 26px;
//     background: white;
//     border-radius: 50%;
//     top: 2px;
//     left: 2px;
//     transition: transform 0.3s ease-in-out;
//   }
  
  
  .form-check-input:checked {
    background-color: #007bff !important; 
  }
  
  .form-check-input:checked::before {
    transform: translateX(30px); 
  }
  