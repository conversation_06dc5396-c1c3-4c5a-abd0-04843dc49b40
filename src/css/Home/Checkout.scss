@use "../theme/var";

.checkout {
    padding: 4rem 0;
    position: relative;

    .checkoutContainer {
        background-color: #F2F2F2;
        padding: 40px 50px;
        border-radius: 30px;

        &_title {
            font-size: 36px;
            font-weight: 700;
            color: var.$clr031940;
            margin-bottom: 10px;
        }

        &_subtitle {
            font-size: 15px;
            font-weight: 600;
            color: var.$black;
        }

        &_top {
            display: flex;
            justify-content: end;

            &_right {
                width: 70%;
                display: flex;
                justify-content: space-between;
                align-items: center;
                gap: 10px;

                p {
                    font-size: 14px;
                    font-weight: 600;
                    margin-top: 10px;
                }

                .border-gray {
                    border-top: 3px solid #808080;
                }

                .border-blue {
                    border-top: 3px solid #04498C;
                }
            }
        }

        &_billing {

            &_title {
                font-size: 24px;
                font-weight: 700;
                color: var.$clr031940;
                margin-bottom: 15px;
            }

            label {
                font-size: 18px;
                color: #000000;
                margin-bottom: 8px;
            }

            input,
            .customSelect {
                background-color: #fff;
                border: 1px solid #00000033;
                color: #00000099;
                font-weight: 600;
            }

            .form-control:focus {
                box-shadow: none;
                background-color: #fff !important;
                border: 1px solid #00000033 !important;
                color: #00000099 !important;
            }

            ::placeholder {
                color: #00000099 !important;
                opacity: 1;
            }

            ::-ms-input-placeholder {
                color: #00000099 !important;
            }

            .customSelect {
                width: 100%;
                min-height: 56px;
                box-shadow: none;
                outline: none;
                padding: .5rem 1.25rem;
                border-radius: 1rem;

                option {
                    color: #00000099;
                }
            }

            .custom_checkbox_input:checked {
                background-color: #00adef !important;
                border: 1px solid #00adef !important;
            }
        }

        &_orderSection {
            background-color: #FFFFFF;
            border: 1px solid #00000033;
            padding: 20px;
            border-radius: 15px;

            p {
                font-size: 18px;
                color: #000;
                font-weight: 600;
            }

            .orderSummary,
            .tax {
                display: flex;
                justify-content: space-between;
                padding-bottom: 20px;
                border-bottom: 1px solid #00000033;
            }

            .subtotal {
                display: flex;
                justify-content: space-between;
                padding-top: 10px;
                padding-bottom: 5px;

                p {
                    font-size: 16px;
                }

                span {
                    color: var.$borderclr;
                    font-weight: 600;
                }
            }

            .tax {
                padding-bottom: 10px;

                p {
                    font-size: 16px;
                }

                span {
                    color: var.$borderclr;
                    font-weight: 600;
                }
            }

            .orderTotal {
                display: flex;
                justify-content: space-between;
                padding-top: 10px;
            }

            &_items {

                &_box {
                    display: flex;
                    justify-content: space-between;
                    padding-bottom: 20px;

                    .itemImg {
                        height: 120px;
                        width: 120px;
                        object-fit: cover;
                    }

                    .itemName {
                        font-size: 18px;
                    }

                    .itemFormat,
                    .itemDuration {
                        color: var.$borderclr;

                        span {
                            color: #000;
                        }
                    }

                    .itemLicense {
                        display: flex;
                        align-items: center;
                        gap: 5px;

                        p {
                            margin-bottom: 0 !important;
                        }
                    }

                    &_right {
                        display: flex;
                        flex-direction: column;
                        align-items: end;

                        p {
                            margin-bottom: 0 !important;
                        }

                        button {
                            color: var.$baseclr;
                            text-decoration: underline;
                            font-weight: bold;
                        }
                    }
                }

                &_box:first-child {
                    padding-top: 20px;
                }
            }
        }

        .AccessOrder {
            display: flex;
            gap: 20px;
            align-items: center;
            margin: 20px 0;

            p {
                font-size: 20px;
                font-weight: 600;
                color: var.$black;
            }

            .border-gray {
                border-top: 1px solid #00000033;
            }
        }

        .earn {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;

            p {
                font-size: 20px;
                font-weight: 600;
                color: var.$black;
            }

            a {
                font-size: 20px;
                font-weight: 600;
                color: var.$baseclr;
            }
        }

        &_earnReward,
        &_trading {
            max-height: 300px;
            min-height: 300px;
            overflow: hidden;
            background-color: #fff;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 0 40px #00000026;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;

            .heading {
                font-size: 20px;
                font-weight: 600;
                color: #000;
            }

            .subHeading {
                font-size: 18px;
                font-weight: 600;
                color: #000;
                text-align: center;
            }

            p {
                font-size: 16px;
                font-weight: 600;
                color: #000;
            }

            span {
                font-weight: 600;
                color: #04498C;
            }

            .button-over-image {
                position: relative;
                width: 100%;
                display: flex;
                justify-content: center;

                img {
                    height: 160px;
                }

                .exploreBtn {
                    position: absolute;
                    width: 100%;
                    top: 40px;
                }
            }
        }
    }
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type=number] {
    -moz-appearance: textfield;
}

@media only screen and (width <=550px) {
    .checkout {
        padding: 2rem 0;
    }

    .checkout .checkoutContainer {
        padding: 20px 10px;
    }

    .checkout .checkoutContainer_title {
        font-size: 28px !important;
    }

    .checkout .checkoutContainer_orderSection_items_box {
        display: block;
    }

    .checkout .checkoutContainer_orderSection_items_box_right {
        flex-direction: row !important;
        justify-content: space-between;

    }
}

@media only screen and (width <=992px) {
    .checkout .checkoutContainer_top_right {
        margin-top: 20px;
        width: 100% !important;
    }

    .checkout .checkoutContainer .earn {
        flex-direction: column;
        align-items: center;
        gap: 5px;
    }

    .checkout .checkoutContainer_earnReward,
    .checkout .checkoutContainer_trading {
        max-height: fit-content;
        min-height: fit-content;
    }
}

.cart_steps {
    width: 55px;
    height: 55px;
    background-color: #fff;
    border-radius: 50%;
    border: 2.5px solid;
    display: flex;
    justify-content: center;
}