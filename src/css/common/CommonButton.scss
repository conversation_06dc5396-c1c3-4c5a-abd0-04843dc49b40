@use "../theme/var";

.btn-style,
.btn-primary {
  min-height: 66px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  border-radius: 10rem;
  padding: 0.5rem 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  background-color: var.$baseclr;
  border: 0;
  text-transform: capitalize;
  transition: all ease-in-out 0.3s;
  min-width: 150px;
  color: var.$white;
  line-height: 1;

  @media (max-width: 1599px) {
    min-height: 66px;
  }

  @media (max-width: 1199px) {
    min-height: 56px;
    font-size: 1.125rem;
    font-weight: 500;
  }

  @media (max-width: 767px) {
    min-height: 46px;
    font-size: 1rem;
  }

  &:hover {
    background-color: var.$baseclrhover;
    color: var.$white;
  }

  &.transparent {
    background-color: transparent;
    border: none;
  }

  &.white-btn {
    background: var.$white;
    color: var.$black;

    &:hover {
      background: var.$baseclr;
      color: var.$white;
    }
  }

  &.yellow-btn {
    background-color: var.$yellow;
    color: var.$white;

    &:hover {
      background-color: var.$yellowBtnHover;
      color: var.$white;
    }
  }

  &.gray-btn {
    background-color: var.$grayBtn !important;
    color: var.$white;

    &:hover {
      background-color: var.$grayBtnHover;
      color: var.$white;
    }
  }

  &.gradient-btn {
    background: linear-gradient(75deg, #00aeef, #1f5aff 50.31%, #da00ff);
    color: var.$white;

    &:hover {
      background: linear-gradient(75deg, #0043ff, #1f5aff 50.31%, #da00ff);
      color: var.$white;
    }
  }

  &.green-btn {
    background-color: var.$green;
    color: var.$white;

    &:hover {
      background-color: var.$greenbtnhover;
      color: var.$white;
    }
  }

  &.red-btn {
    background-color: var.$redlightclr;
    color: var.$white;

    &:hover {
      background-color: var.$redbghover;
      color: var.$white;
    }
  }

  &.border-btn {
    background: transparent;
    color: var.$white;
    border: 1px solid var.$baseclr;

    &:hover {
      background: var.$baseclr;
      color: var.$white;
    }
  }

  .onlyIcon {
    margin-right: 15px;
    display: inline-flex;
  }

  &:disabled,
  &.disabled {
    background: var.$textclr;
    color: var.$white;
    cursor: not-allowed;
    opacity: 1;
  }
}

:disabled,
.disabled {
  background-color: #414c60;
  color: var.$white;
  cursor: not-allowed;
  opacity: 1;
}

.white20 {
  background-color: #ffffff1f;
  width: 100%;
}