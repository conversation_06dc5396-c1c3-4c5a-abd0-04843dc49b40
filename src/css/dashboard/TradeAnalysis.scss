@use "../theme/var";

.trade_analysis {
  &_heading {
    background-color: #0b2c62;
    padding: 10px 30px;
    width: fit-content;
    border-radius: 15px 15px 0 0;
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;

    @media screen and (max-width: 767px) {
      padding: 10px 23px;
    }

    &.active {
      background-color: var.$clr031940;
    }
  }

  &_card {
    background-color: var.$clr031940;
    padding: 20px 15px;
    border-radius: 0 0 25px 25px;

    .drag-handle {
      cursor: grab;
    }

    .heading {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 7px;
      padding: 0px 0px 15px 0px;
      position: relative;

      h2 {
        font-size: 20px;
        font-weight: 700;
      }

      .collapse_main {
        float: right;
        position: absolute;
        right: 0;
        background-color: #00adef;
        height: 23px;
        width: 23px;
        display: flex;
        justify-content: center;
        padding: 5px;
        border-radius: 50%;
        margin-right: 15px;
      }
      @media screen and (min-width: 1024px) {
        .collapse_main {
          display: none;
        }
      }
    }
    @media screen and (max-width: 1440) {
      .heading {
        padding-top: 8px;
      }
    }

    p {
      font-size: 18px;
      font-weight: 400;

      span {
        color: var.$baseclr;
      }
    }

    .innerCard {
      background-color: #283f68;
      padding: 10px;
      border-radius: 0 0 20px 20px;
      border-top: 1px solid #ffffff80;
      margin-bottom: 10px;

      .cardHeading {
        display: flex;
        gap: 10px;
        align-items: center;
        padding-bottom: 10px;
        border-bottom: 1px solid #ffffff80;

        .whiteCircle {
          width: 14px;
          height: 14px;
          border-radius: 50%;
          background-color: #fff;
        }

        p {
          font-size: 16px;
          font-weight: 700;
          padding-left: 16px;
        }
      }

      .blueCard {
        background-color: var.$clr031940;
        padding: 8px 10px;
        border-radius: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        min-height: 40px;

        p {
          font-size: 14px;
          font-weight: 600;
        }
      }
    }
  }

  &_portfolio {
    margin-bottom: 0.5rem;
    padding-bottom: 10px;
    border-bottom: 1px solid #ffffff80;

    span {
      font-size: 14px;
      font-weight: 600;
    }

    .greenCircle {
      width: 16px;
      height: 16px;
      background-color: #32cd33;
      border-radius: 50%;
      flex-shrink: 0;
    }

    .redCircle {
      width: 16px;
      height: 16px;
      background-color: #ff696a;
      border-radius: 50%;
      flex-shrink: 0;
    }
  }
  &_configure {
    .configure_add {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 7px 9px;
      border-bottom: 1px solid #ffffff80;
    }
    .dashed_design {
      background-color: #ffffff1a;
      border: 1px dashed white !important;
      border-radius: 5px;
      height: 16px;
      width: 16px;
      flex-shrink: 0;
    }
    .header_title {
      font-size: 14px;
      font-weight: 600;
    }
    .count_add {
      font-size: 14px;
      font-weight: 600;
    }
  }
  &_strategy {
    .condition-dropdown {
      background-color: #283f68;
      width: 100%;
      border-bottom-left-radius: 20px;
      border-bottom-right-radius: 20px;
      border-bottom: 1px solid #031940;
      position: absolute;
      top: 44;
      left: 0;
      z-index: 100;

      .btns {
        padding: 15px;
        border-top: 1px solid #ffffff33;
        display: flex;
        justify-content: end;
        gap: 10px;
        margin-right: 10px;
        font-size: 16px;
        font-weight: 600;

        .confirm {
          color: #00adef;
        }

        .cancel {
          color: #ffffff;
        }
      }
      .TripleDotBtn {
        height: 26px;
        width: 26px;
        transition: all 0.2s ease-in-out;
        display: grid;
        place-items: center;
        border-radius: 50%;
        flex-shrink: 0;

        &:hover {
          background-color: rgba(208, 207, 207, 0.426);
        }

        &:active {
          background-color: rgba(208, 207, 207, 0.771);
        }
      }
      .dropdownlist {
        display: flex;
        align-items: center;
        gap: 14px;
        padding-top: 5px;
        padding-bottom: 3px;
        font-size: 14px;
        font-weight: 600;

        img {
          width: 15px !important;
          min-width: 15px !important;
        }

        span {
          text-align: left;
          flex: 1;
        }
      }
    }

    .right_side {
      background-color: #fff;
      height: auto;
      width: 100%;
      z-index: -1;

      .scope_dimension_show {
        display: flex;
        gap: 8px;
        align-items: center;
        padding: 10px 10px;
        color: #000000;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        border-bottom: 1px solid #0000000d !important;

        &:hover {
          background-color: #f9f9f9;
        }

        .select_category {
          color: #000000;
          font-size: 16px;
          font-weight: 700;
          padding-left: 5px;
        }
        .custom_checkbox_input {
          height: 20px !important;
          width: 20px !important;
          border: 1px solid #00adef !important;
          border-radius: 5px !important;
          cursor: pointer;
          cursor: pointer;
        }
        .name {
          text-transform: uppercase;
          color: #000000;
          font-size: 14px;
          font-weight: 600;
          cursor: pointer;
        }
      }
    }
    .custom_checkbox_input:checked {
      background-color: #00adef !important;
      border: 1px solid #00adef !important;
    }
    .include_trades {
      background-color: #031940;
      padding: 6px 10px;
      border-radius: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;
      gap: 5px;

      .head {
        display: flex;
        gap: 0.45rem;
        align-items: center;
      }

      img {
        height: 14px;
      }

      &_circle {
        width: 14px;
        height: 14px;
        background-color: white;
        border-radius: 50%;
        flex-shrink: 0;
      }
      span {
        font-size: 14px;
        font-weight: 600;
      }
    }
  }
  &_filter {
    .filter_section {
      background-color: #031940;
      padding: 6px 10px;
      border-radius: 8px;
      margin-top: 8px;

      .include_trades {
        padding: 0px;
        border-radius: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0px;
        margin-bottom: 5px;
        gap: 5px;

        .head {
          display: flex;
          gap: 0.45rem;
          align-items: center;
        }

        img {
          height: 14px;
        }

        .include {
          width: 14px;
          height: 14px;
          background-color: #32cd33;
          border-radius: 50%;
          flex-shrink: 0;
        }
        .exclude {
          width: 14px;
          height: 14px;
          background-color: #ff696a;
          border-radius: 50%;
          flex-shrink: 0;
        }
        span {
          font-size: 14px;
          font-weight: 600;
        }
      }
      .tags_section {
        display: flex;
        flex-wrap: wrap;
      }

      .tag {
        cursor: pointer;
        width: fit-content;
        display: flex;
        align-items: center;
        background: white;
        border-radius: 50px;
        color: #808080;
        padding: 5px 10px;
        gap: 10px;
        font-size: 14px;
        font-weight: 600;
        margin: 5px 0px;
      }

      .condition_section {
        .conditions-wrapper {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .show-condition {
          width: fit-content;
          background-color: #031940;
          border: 1px dashed #ffffff80;
          padding: 5px 20px;
          color: #fff;
          border-radius: 50px;
          font-weight: 600;
          display: flex;
          gap: 10px;
          align-items: center;
          cursor: pointer;
          margin: 5px 0px;

          img {
            height: 15px;
            width: 15px;
          }
        }

        .condition-dropdown {
          position: absolute;
          top: 0;
          left: 0;
          background-color: #031942;
          width: 370px;
          border: 1px solid #00adef;
          padding: 15px 20px;
          border-radius: 20px;
          z-index: 999;

          p {
            font-size: 20px;
            font-weight: 600;
            color: #ffffff;
          }

          .divider {
            border-bottom: 1px solid #fff3;
            margin: 10px 0;
          }

          .select-operation {
            background-color: #fff;
            border-radius: 15px;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;

            span {
              font-weight: 600;
              color: #000;
            }
          }

          .select-operation-dropdown {
            position: absolute;
            top: 0;
            width: 100%;
            background-color: #fff;
            border-radius: 15px;

            span {
              display: block;
              font-weight: 600;
              color: #000;
              padding: 10px 20px;
              cursor: pointer;
            }
          }

          .expression {
            margin-bottom: 10px;
            background-color: #fff;
            border-radius: 15px;
            padding: 10px 20px;

            input {
              font-weight: 600;
              color: #000;
              width: 100%;

              &:focus {
                box-shadow: none;
                outline: 0;
              }

              &::placeholder {
                color: #000;
                opacity: 1;
              }
            }
          }

          .btns {
            padding-top: 10px;
            border-top: 1px solid #ffffff33;
            display: flex;
            justify-content: end;
            gap: 10px;

            .confirm {
              color: #00adef;
            }

            .cancel {
              color: #ffffff;
            }
          }
        }
      }
    }
  }
  &_dimension,
  &_filter,
  &_metrics,
  &_row {
    .condition-dropdown {
      background-color: #283f68;
      width: 100%;
      border-radius: 0px;

      .btns {
        padding-top: 10px;
        border-top: 1px solid #ffffff33;
        display: flex;
        justify-content: end;
        gap: 10px;
        margin-right: 10px;
        font-size: 16px;
        font-weight: 600;

        .confirm {
          color: #00adef;
        }

        .cancel {
          color: #ffffff;
        }
      }
    }

    .custom_checkbox_input:checked {
      background-color: #00adef !important;
      border: 1px solid #00adef !important;
    }
    .include_trades {
      background-color: #031940;
      padding: 6px 10px;
      border-radius: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;
      gap: 5px;
      transition: transform 0.2s ease, opacity 0.2s ease;

      .head {
        display: flex;
        gap: 0.45rem;
        align-items: center;

        h6,
        p {
          font-size: 14px;
          font-weight: 600;
        }
        h6 {
          border-bottom: 1px dashed white;
        }
      }

      img {
        height: 14px;
      }
    }
    .modal_overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background-color: rgba(0, 0, 0, 0.6);
      z-index: 9998;
    }
    .search_section {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 1000;
      width: 1000px;
      background-color: #031940;
      padding: 20px;
      border-radius: 15px;
      max-height: 90vh;
      border: 1px solid #00adef;

      @media (max-width: 1023px) {
        width: 90%;
      }

      h4 {
        font-size: 28px;
        font-weight: 400;
        margin-bottom: 20px;
      }

      .search_header {
        display: flex;
        justify-content: space-between;
        gap: 25px;
        margin-bottom: 15px;

        @media (max-width: 1023px) {
          gap: 15px;
        }

        .closing_Section {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding-right: 16px;
          flex-shrink: 0;

          @media (max-width: 1023px) {
            padding-right: 8px;
          }

          img {
            cursor: pointer;
          }
        }

        p {
          font-size: 18px;
          font-weight: 600;
          white-space: nowrap;
        }
        span {
          white-space: nowrap;
          color: #ffffff99;
        }
      }
      .btn_include {
        background-color: #fff3;
        border-radius: 10px;
        padding: 5px;
        width: 100%;
        font-weight: 600;
        transition: all 0.4s ease-in-out;
        margin: 10px 0px 15px 15px;

        &:hover {
          background-color: #32cd33;
        }
        &.active {
          background-color: #32cd33;
        }
      }
      .btn_exclude {
        background-color: #fff3;
        border-radius: 10px;
        padding: 5px;
        width: 100%;
        font-weight: 600;
        transition: all 0.4s ease-in-out;
        margin: 10px 15px 15px 0px;

        &:hover {
          background-color: #ff696a;
        }
        &.active {
          background-color: #ff696a;
        }
      }
      .search {
        width: 100%;
        background-color: #ffffff33;
        padding: 10px 20px;
        border-radius: 15px;
        display: flex;
        gap: 10px;

        input {
          background-color: transparent;
          width: 100%;
          height: 100%;
          color: #ffffff99;

          &:focus {
            box-shadow: none;
            outline: 0;
          }
        }
      }

      .scope_section {
        display: flex;
        overflow-x: auto;
        border-bottom: 2px solid white;

        .active {
          background-color: #00adef33;
          border-bottom: 2px solid #00adef;
        }

        .scopeName {
          display: flex;
          align-items: center;
          gap: 10px;
          padding: 12px 10px;
          cursor: pointer;
          transition: 0.3s all ease-in-out;

          p {
            font-size: 18px;
            font-weight: 400;
          }

          .scopeCount {
            font-weight: 600;
            background-color: #fff;
            width: fit-content;
            color: #000;
            border-radius: 50px;
            padding: 5px 10px;
          }

          &:hover {
            background-color: #00adef33;
          }
        }
      }

      .scope_content {
        .left_side {
          display: flex;
          padding-right: 20px;

          .active {
            background-color: #00adef33;
            border-bottom: 2px solid #00adef;
          }
          .scopeCount {
            font-weight: 600;
            background-color: #fff;
            width: fit-content;
            color: #000;
            border-radius: 50px;
            padding: 5px 10px;

            @media (max-width: 390px) {
              padding: 4px 7px;
              font-size: 13px;
            }
          }

          .scope_dimension,
          .scope_metrices {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            cursor: pointer;
            transition: 0.3s all ease-in-out;
            gap: 10px;

            @media (max-width: 390px) {
              padding: 15px 10px;
            }

            &:hover {
              background-color: #00adef33;
            }

            svg {
              transform: rotate(-90deg);
            }
          }
        }

        .right_side {
          background-color: #fff;
          width: 100%;
          overflow-y: auto;
          height: 300px;
          min-height: calc(90vh - 360px) !important;
          max-height: calc(90vh - 360px) !important;

          .default {
            font-size: 20px;
            color: #000;
            font-weight: 600;
            text-align: center;
          }

          .scope_dimension_show,
          .scope_metrices_show {
            display: flex;
            gap: 20px;
            align-items: center;
            padding: 10px 20px;
            color: #000000;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            border-bottom: 1px solid #0000000d !important;

            &:hover {
              background-color: #f9f9f9;
            }
            .custom_checkbox_input {
              height: 20px !important;
              width: 20px !important;
              border: 1px solid #00adef !important;
              border-radius: 5px !important;
              cursor: pointer;
            }

            .name {
              text-transform: uppercase;
              color: #000000;
              font-size: 16px;
              font-weight: 600;
              cursor: pointer;
            }
          }
          .active_row {
            background-color: rgb(224 224 224);

            &:hover {
              background-color: rgb(224 224 224);
            }
          }
        }
      }
      @keyframes fadeOut {
        0% {
          opacity: 0.9;
        }
        50% {
          opacity: 1;
        }
        90% {
          opacity: 0.9;
        }
        100% {
          opacity: 0;
        }
      }
    }
  }
  &_row {
    .condition-dropdown {
      background-color: #283f68;
      width: 100%;
      border-radius: 0px;

      .btns {
        padding-top: 10px;
        border-top: 1px solid #ffffff33;
        display: flex;
        justify-content: end;
        gap: 10px;
        margin-right: 10px;
        font-size: 16px;
        font-weight: 600;

        .confirm {
          color: #00adef;
        }

        .cancel {
          color: #ffffff;
        }
      }
    }

    .custom_checkbox_input:checked {
      background-color: #00adef !important;
      border: 1px solid #00adef !important;
    }
    .include_trades {
      background-color: #031940;
      padding: 6px 10px;
      border-radius: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;
      gap: 5px;
      // max-height: 53px !important;
      // background-color: red;

      .head {
        display: flex;
        gap: 0.45rem;
        align-items: center;

        h6,
        p {
          font-size: 14px;
          font-weight: 600;
        }
        h6 {
          border-bottom: 1px dashed white;
        }
      }

      img {
        height: 14px;
      }
    }
    .pagination_row {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-top: 10px;
      position: relative;

      p {
        font-size: 14px;
        font-weight: 600;
        padding-left: 9px;
      }

      .custom-select {
        font-size: 14px;
        font-family: 600;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        background-color: #031940;
        color: white;
        padding: 4px 26px 4px 7px;
        border: none;
        border-radius: 6px;
        background-image: url("http://cdn.tradereply.com/dev/site-assets/icons/tradereply-white-down-arrow.svg");
        background-repeat: no-repeat;
        background-position: right 7px center;
        background-size: 14px;
        cursor: pointer;
      }
    }
    .empty-state {
      border: 1px dashed white;
      border-radius: 8px;
      padding: 14px 15px;
      margin-top: 10px;
      display: flex;
      gap: 10px;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease-in-out;

      &:hover {
        background-color: #172d55;
      }

      p {
        font-size: 14.5px;
      }
    }
  }
  &_setup {
    &_btns {
      display: flex;
      justify-content: end;
      gap: 12px;

      @media (max-width: 1023px) {
        margin-bottom: 20px;
      }

      @media (max-width: 500px) {
        button {
          width: 100% !important;
          flex: 1 1 0;
          min-width: 120px;
          max-width: calc(50% - 0.25rem);
        }
      }
    }

    .innerCard {
      padding: 0px;
    }
    .saved_popup {
      position: absolute;
      top: 48;
      left: 0;
      z-index: 1000;
    }
    .save_reports {
      padding: 12px 10px 14px 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      position: relative;

      &::after {
        content: "";
        display: block;
        background-image: url("http://cdn.tradereply.com/dev/site-assets/icons/tradereply-white-down-arrow.svg");
        background-repeat: no-repeat;
        background-position: center;
        width: 1.15rem;
        height: 1.15rem;
        border: 0;
        transition: all ease-in-out 0.3s;
      }
    }
    .rotate-icon {
      &::after {
        transform: rotate(-90deg);
      }
    }
    .report_name,
    .report_desc,
    .save_reports {
      label {
        font-size: 16px;
        font-weight: 600;
      }
      input,
      textarea {
        background-color: #366da3;
        width: 100%;
        border-radius: 8px;
        font-weight: 600;
        padding: 5px 10px 5px 11px;
        margin: 11px 0px 0px 0px;

        &:focus {
          box-shadow: none;
          outline: 0;
        }
        &::placeholder {
          color: White;
        }
      }
      .character-count {
        text-align: end;
        color: white;
        font-size: 14px;
      }
    }
    .dropdownlist {
      display: flex;
      align-items: center;
      gap: 14px;
      padding-top: 5px;
      padding-bottom: 3px;
      font-size: 14px;
      font-weight: 600;

      img {
        width: 15px !important;
        min-width: 15px !important;
      }

      span {
        text-align: left;
        flex: 1;
      }
    }

    .condition-dropdown {
      width: 100%;
      border-radius: 0px;
    }
    .right_side {
      background-color: #fff;
      height: auto;
      width: 100%;
      z-index: -1;
      border-radius: 0 0 20px 20px;

      .scope_dimension_show {
        display: flex;
        gap: 8px;
        justify-content: space-between;
        align-items: center;
        padding: 12px 8px 12px 12px;
        color: #000000;
        font-size: 16px;
        font-weight: 600;
        border-bottom: 1px solid #0000000d !important;
        cursor: pointer;

        &:last-child {
          border-radius: 0 0 20px 20px;
        }
        &:hover {
          background-color: #f9f9f9;
        }

        .select_category {
          color: #000000;
          font-size: 14px;
          font-weight: 700;
          display: inline;
        }
        .name,
        .report_time {
          text-transform: uppercase;
          color: #000000;
          font-size: 14px;
          font-weight: 600;
        }
        .report_time {
          white-space: nowrap;
        }

        .TripleDotBtn {
          height: 26px;
          width: 25px;
          transition: all 0.2s ease-in-out;
          display: grid;
          place-items: center;
          border-radius: 50%;

          &:hover {
            background-color: rgba(208, 207, 207, 0.426);
          }

          &:active {
            background-color: rgba(208, 207, 207, 0.771);
          }
        }
      }
    }
  }
  &_view {
    .large_tabel_view {
      thead {
        th {
          vertical-align: middle;
          background: #172d54;
          font-size: 16px;
          font-weight: 600;
          color: #ffffff;
          padding: 9px;
          text-align: end;
          border: 1px solid #cccccc !important;
        }
        p {
          font-size: 16px;
          font-weight: 600;
          white-space: nowrap;
        }
        th:nth-child(1),
        th:nth-child(2) {
          text-align: start;
        }
        th:nth-child(1),
        th:nth-child(5) {
          border-right: 0 !important;
          border-left: 0 !important;
        }
      }

      tbody {
        tr td:first-child {
          border-left: 0;
        }
        tr:last-child td,
        tr:last-child {
          border-bottom: 0;
        }
        tr:last-child td:first-child,
        tr:last-child {
          border-bottom-left-radius: 20px;
          border: 0;
        }

        tr:last-child td:last-child {
          border-bottom-right-radius: 20px;
          border: 0;
        }

        td {
          font-size: 18px;
          font-weight: 400;
          text-align: end;
          border: 1px solid #cccccc;
          padding: 10px;
          white-space: nowrap;
        }
        td:nth-child(1),
        td:nth-child(2) {
          background-color: #1a1a1a;
          color: white;
          text-align: start;
          border: 1px solid cccccc;
        }
      }
    }

    .mini_screen {
      .firstHeader,
      .thirdHeader {
        position: relative;
        background: #172d54;
        font-size: 14px;
        font-weight: 600;
        color: #ffffff;
        padding: 25px 10px 25px 12px;
        border-top: 1px solid #cccccc !important;
        border-bottom: 1px solid #cccccc !important;
        text-align: center;

        &::before {
          content: "";
          position: absolute;
          top: 50%;
          left: 11px;
          transform: translateY(-50%);
          background-image: url("https://cdn.tradereply.com/dev/site-assets/icons/tradereply-single-stack.svg");
          background-repeat: no-repeat;
          background-position: center;
          background-size: contain;
          width: 1.15rem;
          height: 1.15rem;
          transition: all ease-in-out 0.3s;
        }
      }
      .collapse_header {
        .secondHeader:last-child {
          border-bottom-right-radius: 20px;
          border-bottom-left-radius: 20px;
          border-bottom: 0 !important;
        }
      }
      .secondHeader {
        background-color: #1a1a1a;
        color: white;
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        font-weight: 600;
        border-top: 1px solid white !important;
        border-bottom: 1px solid white !important;
        padding: 17px 15px 17px 12px;

        &:nth-child(even) {
          background-color: #2e2e2e;
        }
      }
      .thirdHeader {
        padding: 15px 15px 15px 12px !important;

        &::before {
          content: "";
          background-image: url("https://cdn.tradereply.com/dev/site-assets/icons/tradereply-triple-stack.svg");
        }
      }
      .firstValue {
        background-color: white;
        color: black;
        font-size: 14px;
        font-weight: 600;
        border-top: 1px solid white !important;
        border-bottom: 1px solid white !important;
        padding: 15px 15px 15px 12px;
        text-align: center;
      }

      table {
        margin-bottom: 0px;
        thead {
          th {
            vertical-align: middle;
            background: #172d54;
            font-size: 13px;
            font-weight: 600;
            color: #ffffff;
            padding: 9px 15px;
            border: 1px solid #cccccc !important;
            text-align: center;
            vertical-align: middle;
            img {
              display: inline;
              margin-right: 15px;
            }
          }
          th:nth-child(1) {
            border-left: 0 !important;
          }
          th:nth-child(2) {
            border-right: 0 !important;
          }
        }

        tbody {
          tr td:first-child {
            border-left: 0;
          }
          tr:last-child td,
          tr:last-child {
            border-bottom: 0;
          }
          tr:last-child td:first-child,
          tr:last-child {
            border: 0;
          }

          tr:last-child td:last-child {
            border: 0;
          }

          td {
            font-size: 13px;
            text-align: center;
            vertical-align: middle;
            border: 1px solid #cccccc;
            padding: 9px 15px;
          }
          td:nth-child(1) {
            font-weight: 400;
            background-color: #1a1a1a;
            color: white;
            border: 1px solid cccccc;
          }
          td:nth-child(2) {
            font-weight: 600;
            background-color: white;
            color: black;
            border: 1px solid cccccc;
          }
        }
      }
    }
  }
}
