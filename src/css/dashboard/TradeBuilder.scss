@use "../theme/var";

.trade_builder {
  &_card {
    padding: 1rem;
    background: var.$gradientcardblue;
    border: 2px solid var.$baseclr;
    border-radius: 1.25rem;

    @media screen and (max-width: 991px) {
      padding: 1rem;
    }

    &.greengrandient {
      border: 0;

      .trade_builder_card_head_btnArrow {
        svg {
          path {
            fill: var.$white !important;
          }
        }
      }

      svg {
        path {
          fill: var.$yellow !important;
        }
      }
    }

    &_head {
      position: relative;

      h2 {
        font-size: 1.5rem !important;
      }
      &_btnArrow {
        position: absolute;
        top: -1px;
        right: 1.25rem;
        width: 38px;
        height: 38px;
        border-radius: 10rem;
        border: 0;
        background-color: var.$baseclr;
        display: flex;
        align-items: center;
        justify-content: center;

        @media screen and (max-width: 1199px) {
          width: 40px;
          height: 40px;
          top: 0;
        }

        @media screen and (max-width: 991px) {
          width: 30px;
          height: 30px;
        }

        svg {
          transition: all ease-in-out 0.3s;

          @media screen and (max-width: 1199px) {
            width: 20px;
          }

          @media screen and (max-width: 991px) {
            width: 15px;
          }
        }

        &:hover {
          background-color: var.$baseclrhover;

          // svg {
          //   transform: rotate(180deg);
          // }
        }
      }
    }

    &_body {
      margin-top: 40px;

      &_notes{
        .form-textarea-label{
          color: black;
          font-size: 20px;
          font-weight: 600;
        }
        .form-textarea {
          padding: 1rem;
          border-radius: 15px;
          border: none;
          width: 100%;
          color: white;
          font-size: 18px;
          background-color: #4A91C6;

          @media (max-width: 501px) {
            font-size: 15px;
          }
          &:focus {
            box-shadow: none;
            outline: 0;
          }
          &::placeholder {
            color: White;
          }
        }
        .character-count {
          font-size: 0.9rem;
          font-weight: 600;
          color: white;
          position: absolute;
          bottom: 21;
          right: 11;
        }
      }

      &_box {
        display: flex;
        flex-direction: column;
        flex: 1;
        min-height: 100%;
        margin-bottom: 1.25rem;

        .input-container {
          flex: 1;
          display: flex;
          flex-direction: column;
        }

        .full-height-input {
          height: 100%;
          width: 100%;
          box-sizing: border-box;
        }

        .dropdown-select {
          width: 100%;
          min-height: 100%;
          box-sizing: border-box;
          padding: 0px 10px;
        }

        .head {
          background-color: var.$clr031940;
          padding: 5px 15px;
          border-top-left-radius: 10px;
          border-top-right-radius: 10px;
          color: var.$white;
          display: flex;
          justify-content: space-between;
          align-items: center;
          text-transform: uppercase;

          h5 {
            line-height: normal;
            font-size: 1rem;
            font-weight: 700;

            @media screen and (max-width: 991px) {
              font-size: 14px;
              padding: 10px 10px;
            }
          }

          .subTooltip {
            text-transform: none;
          }
        }

        .show_metrics_dimenstion {
          font-size: 14px;
          padding: 0 15px;
          border-radius: 0;
          background-color: #031940;
          border-top: 1px solid #9cb1d7;
          color: #f3f3f3;
          display: flex;
          gap: 5px;
          align-items: center;
        }

        h4,
        input {
          background-color: var.$white;
          padding: 5px 15px;
          border-bottom-left-radius: 10px;
          border-bottom-right-radius: 10px;
          color: var.$black;
          font-size: 1.2rem;
          font-weight: 700;
          margin: 0;
          line-height: normal;
          width: 100%;
          height: 100%;
          box-sizing: border-box;
        }

        input:focus-visible {
          outline: none !important;
        }

        .card {
          background-color: var.$white;
          padding: 14px 15px;
          border-bottom-left-radius: 10px;
          border-bottom-right-radius: 10px;
          color: var.$black;
          font-weight: 700;
          margin: 0;
          line-height: normal;

          @media screen and (max-width: 991px) {
            font-size: 18px;
            padding: 10px 10px;
          }
        }
      }

      &_note {
        h5 {
          margin-bottom: 10px;
        }

        .note_box {
          background-color: rgba($color: var.$white, $alpha: 0.2);
          border-radius: 15px;
          padding: 1.25rem;

          p {
            font-size: 1.125rem;
            font-weight: 500;
            line-height: 27px;
            margin-bottom: 0.625rem;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }

    .trade_manager_trade_entry_box {
      padding: 0 10px;
      justify-content: flex-start;

      .green_arrow {
        svg {
          path {
            fill: var.$green !important;
          }
        }
      }
    }

    .switch {
      position: relative;
      display: inline-block;
      width: 34px;
      height: 20px;
      margin-left: 10px;
      vertical-align: middle;
      margin-left: auto;

      .switch input {
        opacity: 0;
        width: 0;
        height: 0;
      }

      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: 0.4s;
        border-radius: 34px;
      }

      .slider:before {
        position: absolute;
        content: "";
        height: 14px;
        width: 14px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: 0.4s;
        border-radius: 50%;
      }

      input:checked + .slider {
        background-color: #2196f3;
      }

      input:checked + .slider:before {
        transform: translateX(14px);
      }
    }
    .formula-icon {
      margin-left: 13px;
    }
    .dropdown-select {
      width: 100%;
      height: 33px;
      border-bottom-right-radius: 6px;
      border-bottom-left-radius: 6px;
      font-weight: bold;
      
      option {
        font-weight: bold;
      }
    }
    .modal_overlay {
      .search_section {
        width: 50% !important;
        min-height: auto !important;
        max-height: auto !important;
      }
    }
  }

  .modal_overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 9998;
  }

  .search_section {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    width: 60%;
    background-color: #031940;
    padding: 20px;
    border-radius: 15px;
    min-height: 90vh;
    max-height: 90vh;
    border: 1px solid #00adef;

    @media only screen and (width <=1024px) {
      width: 80%;
    }

    @media only screen and (width <=500px) {
      width: 90%;
    }

    .search_header_mobile {
      .closeModal {
        cursor: pointer;
        width: 20px;
      }

      .selected {
        white-space: nowrap;
        color: #ffffff99;
      }

      .btn-style,
      .btn-primary {
        min-width: 100px !important;
      }
    }

    .search_header {
      display: flex;
      justify-content: space-between;
      gap: 25px;
      margin-bottom: 15px;

      .closeModal {
        cursor: pointer;
        width: 20px;
      }

      p {
        font-size: 18px;
        font-weight: 600;
        white-space: nowrap;
      }

      .selected {
        white-space: nowrap;
        color: #ffffff99;
      }
    }

    .search {
      width: 100%;
      background-color: #ffffff33;
      padding: 10px 20px;
      border-radius: 15px;
      display: flex;
      gap: 10px;

      input {
        background-color: transparent;
        width: 100%;
        height: 100%;
        color: #ffffff99;

        &:focus {
          box-shadow: none;
          outline: 0;
        }
      }
    }

    .scope_section_wrapper {
      position: relative;
      display: flex;
      align-items: center;
      border-bottom: 2px solid white;
    }

    .scope_section {
      height: 60px;
      position: relative;
      display: flex;
      align-items: center;
      overflow-x: auto;
      scroll-behavior: smooth;

      &::-webkit-scrollbar {
        display: none;
      }

      .active {
        background-color: #00adef33;
        border-bottom: 2px solid #00adef;
      }

      .scopeName {
        height: 60px;
        white-space: nowrap;
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 12px 10px;
        cursor: pointer;
        transition: 0.3s all ease-in-out;

        p {
          font-size: 18px;
          font-weight: 400;

          @media (width <=550px) {
            font-size: 14px;
          }

          @media (551px <=width <=900px) {
            font-size: 16px;
          }
        }

        .scopeCount {
          font-weight: 600;
          background-color: #fff;
          width: fit-content;
          color: #000;
          border-radius: 50px;
          padding: 5px 10px;

          @media (width <=550px) {
            font-size: 14px;
          }

          @media (551px <=width <=900px) {
            font-size: 16px;
          }
        }

        &:hover {
          background-color: #00adef33;
        }
      }
    }

    .scope_content {
      .left_side {
        padding-right: 20px;
        position: relative;
        display: flex;
        align-items: center;
        width: 100%;
        height: 60px;

        &::-webkit-scrollbar {
          display: none;
        }

        .scrollable_tabs {
          display: flex;
          overflow-x: auto;
          scroll-behavior: smooth;
          flex: 1;

          &::-webkit-scrollbar {
            display: none;
          }
        }

        .active {
          background-color: #00adef33;
          border-bottom: 2px solid #00adef;
        }

        .scopeCount {
          font-weight: 600;
          background-color: #fff;
          width: fit-content;
          color: #000;
          border-radius: 50px;
          padding: 5px 10px;

          @media (width <=550px) {
            font-size: 14px;
          }

          @media (551px <=width <=900px) {
            font-size: 16px;
          }
        }

        .scope_dimension,
        .scope_metrices {
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px 20px;
          cursor: pointer;
          transition: 0.3s all ease-in-out;
          gap: 10px;

          p {
            font-size: 18px;
            font-weight: 400;

            @media (width <=550px) {
              font-size: 14px;
            }

            @media (551px <=width <=900px) {
              font-size: 16px;
            }
          }

          &:hover {
            background-color: #00adef33;
          }

          svg {
            transform: rotate(-90deg);
          }
        }
      }

      .right_side {
        background-color: #fff;
        width: 100%;
        min-height: calc(90vh - 250px);
        max-height: calc(90vh - 250px);
        overflow-y: auto;

        @media (width <=1200px) {
          min-height: calc(90vh - 295px);
          max-height: calc(90vh - 295px);
        }

        .default {
          font-size: 20px;
          color: #000;
          font-weight: 600;
          text-align: center;
        }

        .scope_dimension_show,
        .scope_metrices_show {
          display: flex;
          gap: 20px;
          align-items: center;
          padding: 10px 20px;
          color: #000000;
          font-size: 16px;
          font-weight: 600;
          background-color: #fff;
          border-bottom: 1px solid #0000000d !important;
          cursor: pointer;

          @media (width <=550px) {
            font-size: 14px;
          }

          &:hover {
            background-color: #f9f9f9;
          }

          .custom_checkbox_input {
            border: 1px solid #00adef !important;
            max-width: 20px;
            min-width: 20px;
            max-height: 20px;
            min-height: 20px;

            &:focus {
              box-shadow: none !important;
            }

            &:checked {
              background-color: #00adef !important;
              border-color: #00adef !important;
            }
          }

          .name {
            text-transform: uppercase;
            color: #000000;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
          }
        }
      }
    }

    .move-pre-arrow {
      position: fixed;
      left: 12px;
      z-index: 1;
      max-width: 40px;
      min-width: 40px;
      height: 60px;
      background-color: #031940;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      .icon {
        max-width: 30px;
        min-width: 30px;
        max-height: 30px;
        min-height: 30px;
        border-radius: 50%;
        background-color: #00adef;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          transform: rotate(-180deg);
        }
      }
    }

    .move-next-arrow {
      position: fixed;
      right: 12;
      z-index: 1;
      max-width: 40px;
      min-width: 40px;
      height: 60px;
      background-color: #031940;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      .icon {
        max-width: 30px;
        min-width: 30px;
        max-height: 30px;
        min-height: 30px;
        border-radius: 50%;
        background-color: #00adef;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}

.trade_head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // margin-bottom: 30px;
  position: relative;

  @media screen and (max-width: 991px) {
    justify-content: unset;
  }

  .common_heading {
    h2 {
      @media screen and (max-width: 1599px) {
        font-size: 35px;
      }

      @media screen and (max-width: 1279px) {
        font-size: 28px;
      }
    }
  }

  &_title {
    display: flex;
    flex-wrap: wrap;

    @media screen and (max-width: 767px) {
      justify-content: center;
      margin-top: 20px;
    }

    h4 {
      margin-right: 10px;
      margin-bottom: 10px;

      @media screen and (max-width: 1599px) {
        font-size: 18px;
        line-height: normal;
      }
    }
  }

  .head_draft {
    &_icons {
      display: flex;
      align-items: center;
      gap: 5px;
      margin-bottom: 5px;

      svg {
        width: 20px;
        height: 20px;
      }
    }
  }

  &_btns {
    justify-content: flex-end;

    @media screen and (max-width: 767px) {
      margin-top: 22px;
      display: flex;
      justify-content: center;
    }

    .btn-style {
      text-transform: capitalize;
      min-width: 100px;
      min-height: 55px;
      font-size: 1.25rem;

      @media screen and (max-width: 1499px) {
        min-width: auto;
        font-size: 16px;
        padding: 5px 25px;
      }

      @media screen and (max-width: 1279px) {
        min-width: auto;
        min-height: 45px;
        font-size: 14px;
        padding: 5px 15px;
      }

      @media screen and (max-width: 991px) {
        min-width: auto;
        width: 33.33%;
      }
    }
  }
}
