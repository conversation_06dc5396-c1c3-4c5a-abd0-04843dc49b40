/**
 * Enhanced React Hook for Security Cookie Monitoring
 * 
 * Provides automatic security cookie monitoring with real-time expiration detection
 */

import { useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import securityCookieMonitor from '@/utils/securityCookieMonitor';
import { fetchSecurityConfig } from '@/utils/securityConfig';

/**
 * Hook to monitor security cookies on secure pages with real-time expiration detection
 * 
 * @param {boolean} enabled - Whether to enable monitoring (default: true)
 * @param {Object} options - Configuration options
 * @param {string} options.fallbackUrl - URL to redirect to if cookie is invalid (default: '/account/overview')
 * @param {number} options.checkInterval - How often to check cookie in milliseconds (default: 5000)
 * @param {number} options.expiryMinutes - Expected cookie expiry time in minutes (default: 1)
 */
export function useSecurityCookieMonitor(enabled = true, options = {}) {
    const router = useRouter();
    const optionsRef = useRef(options);
    
    // Update options ref when options change
    useEffect(() => {
        optionsRef.current = options;
    }, [options]);

    useEffect(() => {
        if (!enabled) {
            return;
        }

        const initializeMonitoring = async () => {
            const {
                fallbackUrl = '/account/overview',
                checkInterval,
                expiryMinutes = 1
            } = optionsRef.current;

            // Load security configuration from backend
            let config;
            try {
                config = await fetchSecurityConfig();
            } catch (error) {
                console.warn('Failed to load security config, using defaults');
                config = {
                    check_interval_milliseconds: 5000,
                    expires_in_minutes: 1
                };
            }

            // Use config values, but allow options to override
            const finalCheckInterval = checkInterval || config.check_interval_milliseconds;
            const finalExpiryMinutes = expiryMinutes || config.expires_in_minutes;

            // Configure monitor with final values
            if (finalCheckInterval) {
                securityCookieMonitor.checkInterval = finalCheckInterval;
            }

            // Override redirect handler to use Next.js router
            const originalHandleInvalidCookie = securityCookieMonitor.handleInvalidCookie ?
                securityCookieMonitor.handleInvalidCookie.bind(securityCookieMonitor) :
                null;

            securityCookieMonitor.handleInvalidCookie = function(reason = 'Invalid or expired security cookie') {
                this.stopMonitoring();

                // Determine redirect URL with priority order:
                // 1. 'from' query parameter (if valid)
                // 2. document.referrer (if valid)
                // 3. fallback URL
                const currentPath = window.location.pathname;
                let redirectUrl = fallbackUrl;

                // Priority 1: Check for 'from' query parameter in current URL
                const fromParam = this.getFromParameter();
                if (fromParam && this.isValidInternalPath(fromParam)) {
                    redirectUrl = fromParam;
                    if (this.debugMode) {
                        console.warn(`[SecurityCookieMonitor] ${reason} - Using 'from' parameter: ${redirectUrl}`);
                    }
                } else {
                    // Priority 2: Use referrer if it's from the same origin and not a security page
                    const referrer = document.referrer;
                    if (referrer && this.isValidReferrer(referrer)) {
                        try {
                            const referrerUrl = new URL(referrer);
                            if (referrerUrl.origin === window.location.origin) {
                                const referrerPath = referrerUrl.pathname + referrerUrl.search + referrerUrl.hash;
                                // Don't redirect to the same page we're currently on
                                if (referrerPath !== currentPath) {
                                    redirectUrl = referrerPath;
                                }
                            }
                        } catch (e) {
                            // Failed to parse referrer URL, use fallback
                        }
                    }

                    // Show user-friendly message if in development
                    if (this.debugMode) {
                        const source = document.referrer && this.isValidReferrer(document.referrer) ? 'referrer' : 'fallback';
                        console.warn(`[SecurityCookieMonitor] ${reason} - Using ${source}: ${redirectUrl}`);
                    }
                }

                // Use Next.js router for navigation
                router.push(redirectUrl);
            };

            // Start monitoring with expiry tracking
            await securityCookieMonitor.startMonitoring(finalExpiryMinutes);

            // Return cleanup function for this initialization
            return () => {
                securityCookieMonitor.stopMonitoring();
                // Restore original handler if it existed
                if (originalHandleInvalidCookie) {
                    securityCookieMonitor.handleInvalidCookie = originalHandleInvalidCookie;
                }
            };
        };

        // Initialize monitoring and store cleanup function
        let cleanup = null;
        initializeMonitoring().then((cleanupFn) => {
            cleanup = cleanupFn;
        }).catch((error) => {
            console.error('Failed to initialize security monitoring:', error);
        });

        // Return cleanup function
        return () => {
            if (cleanup) {
                cleanup();
            }
        };
    }, [enabled, router]);

    return {
        isMonitoring: securityCookieMonitor.isMonitoring,
        stopMonitoring: () => securityCookieMonitor.stopMonitoring(),
        startMonitoring: (expiryMinutes = 1) => securityCookieMonitor.startMonitoring(expiryMinutes),
        checkCookie: () => securityCookieMonitor.checkCookie()
    };
}

export default useSecurityCookieMonitor;
