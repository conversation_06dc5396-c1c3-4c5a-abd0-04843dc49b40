/**
 * Security Configuration Utility
 * 
 * Fetches dynamic security configuration from the backend
 */

import { get } from '@/utils/apiUtils';

// Cache for security configuration
let configCache = null;
let configCacheTime = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Fetch security configuration from backend
 * @returns {Promise<Object>} Security configuration object
 */
export async function fetchSecurityConfig() {
    try {
        // Check if we have a valid cached config
        if (configCache && configCacheTime && (Date.now() - configCacheTime < CACHE_DURATION)) {
            return configCache;
        }

        const response = await get('/security-verification/config');
        
        if (response.success && response.config) {
            configCache = response.config;
            configCacheTime = Date.now();
            return response.config;
        } else {
            throw new Error('Invalid response from security config endpoint');
        }
    } catch (error) {
        console.warn('Failed to fetch security config from backend, using defaults:', error);
        
        // Return default configuration if backend fails
        const defaultConfig = {
            cookie_name: 'security_verified',
            expires_in_minutes: 1,
            check_interval_seconds: 5,
            check_interval_milliseconds: 5000,
        };
        
        configCache = defaultConfig;
        configCacheTime = Date.now();
        return defaultConfig;
    }
}

/**
 * Get cached security configuration (synchronous)
 * @returns {Object|null} Cached configuration or null if not available
 */
export function getCachedSecurityConfig() {
    if (configCache && configCacheTime && (Date.now() - configCacheTime < CACHE_DURATION)) {
        return configCache;
    }
    return null;
}

/**
 * Clear the configuration cache
 */
export function clearSecurityConfigCache() {
    configCache = null;
    configCacheTime = null;
}

/**
 * Get default security configuration
 * @returns {Object} Default configuration object
 */
export function getDefaultSecurityConfig() {
    return {
        cookie_name: 'security_verified',
        expires_in_minutes: 1,
        check_interval_seconds: 5,
        check_interval_milliseconds: 5000,
    };
}
