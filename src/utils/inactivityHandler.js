"use client";

import { useEffect, useRef, useState } from "react";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";

const MAX_INACTIVITY_DURATION = 30 * 60 * 1000;

const InactivityHandler = () => {
  const router = useRouter();
  const timeoutRef = useRef(null);
  const [hasLoggedOut, setHasLoggedOut] = useState(false);

  const logoutUser = () => {
    if (hasLoggedOut) return;
    setHasLoggedOut(true);

    const token = Cookies.get("authToken");

  // ✅ Only set "sessionExpired" if token existed (user was logged in)
  const shouldSetSessionExpired = !!token;

    fetch("/api/logout", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
    }).finally(() => {
      Cookies.remove("authToken");
      localStorage.removeItem("lastActivity");
      // sessionStorage.setItem("sessionExpired", "true");
      if (shouldSetSessionExpired) {
        sessionStorage.setItem("sessionExpired", "true");
      }
      localStorage.setItem("loggedOut", Date.now());
      router.replace("/login");
    });
  };

  const updateLastActivity = () => {
    const now = Date.now();
    localStorage.setItem("lastActivity", now.toString());

    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(logoutUser, MAX_INACTIVITY_DURATION);
  };

  const checkInactivity = () => {
    const lastActivity = parseInt(localStorage.getItem("lastActivity") || "0", 10);
    const now = Date.now();

    if (!lastActivity || now - lastActivity > MAX_INACTIVITY_DURATION) {
      logoutUser();
    } else {
      updateLastActivity();
    }
  };

  useEffect(() => {
    checkInactivity();

    const handleActivity = () => updateLastActivity();

    const activityEvents = ["mousemove", "keydown", "click", "scroll"];
    activityEvents.forEach((event) => window.addEventListener(event, handleActivity));

    const handleStorage = (e) => {
      if (e.key === "loggedOut") {
        logoutUser();
      }
    };
    window.addEventListener("storage", handleStorage);

    return () => {
      activityEvents.forEach((event) => window.removeEventListener(event, handleActivity));
      window.removeEventListener("storage", handleStorage);
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, []);

  return null;
};

export default InactivityHandler;




